/**
 * 简单的内存缓存实现
 * 用于缓存 API 响应，提升页面加载速度
 */

import { NextResponse } from 'next/server';

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class MemoryCache {
  private cache = new Map<string, CacheItem<any>>();

  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param ttlSeconds 过期时间（秒）
   */
  set<T>(key: string, data: T, ttlSeconds: number = 300): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttlSeconds * 1000
    };
    
    this.cache.set(key, item);
    
    // 设置自动清理
    setTimeout(() => {
      this.cache.delete(key);
    }, item.ttl);
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存数据或 null
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data as T;
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// 创建全局缓存实例
export const apiCache = new MemoryCache();

/**
 * 缓存装饰器函数
 * 用于包装 API 处理函数，自动处理缓存逻辑
 */
export function withCache<T>(
  cacheKey: string,
  ttlSeconds: number = 300
) {
  return function(handler: () => Promise<T>) {
    return async (): Promise<T> => {
      // 尝试从缓存获取
      const cached = apiCache.get<T>(cacheKey);
      if (cached !== null) {
        console.log(`缓存命中: ${cacheKey}`);
        return cached;
      }

      // 缓存未命中，执行原始处理函数
      console.log(`缓存未命中，执行查询: ${cacheKey}`);
      const result = await handler();
      
      // 存储到缓存
      apiCache.set(cacheKey, result, ttlSeconds);
      
      return result;
    };
  };
}

/**
 * 创建带缓存的 NextResponse
 * 同时设置 HTTP 缓存头
 */
export function createCachedResponse<T>(
  data: T,
  cacheSeconds: number = 300
) {
  const response = NextResponse.json(data);
  
  // 设置 HTTP 缓存头
  response.headers.set(
    'Cache-Control',
    `public, max-age=${cacheSeconds}, s-maxage=${cacheSeconds}`
  );
  
  // 设置 ETag 用于条件请求
  const etag = `"${Date.now()}"`;
  response.headers.set('ETag', etag);
  
  return response;
}

/**
 * 缓存失效函数
 * 当数据更新时调用，清除相关缓存
 */
export function invalidateCache(patterns: string[]) {
  patterns.forEach(pattern => {
    // 支持通配符匹配
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      const keysToDelete = Array.from(apiCache.getStats().keys).filter(key => 
        regex.test(key)
      );
      keysToDelete.forEach(key => apiCache.delete(key));
      console.log(`缓存失效 (模式匹配): ${pattern}, 清除了 ${keysToDelete.length} 个缓存`);
    } else {
      apiCache.delete(pattern);
      console.log(`缓存失效: ${pattern}`);
    }
  });
}

// 预定义的缓存键
export const CACHE_KEYS = {
  CATEGORIES: 'api:categories:all',
  TAGS: 'api:tags:all',
  PROMPTS: 'api:prompts:all',
  CATEGORY: (id: string) => `api:categories:${id}`,
  TAG: (id: string) => `api:tags:${id}`,
  PROMPT: (id: string) => `api:prompts:${id}`,
} as const;

// 缓存配置
export const CACHE_CONFIG = {
  // 读取操作缓存时间（秒）
  READ_TTL: {
    CATEGORIES: 300, // 5分钟
    TAGS: 300,       // 5分钟  
    PROMPTS: 60,     // 1分钟（更新频繁）
  },
  
  // 写入操作后需要失效的缓存模式
  INVALIDATION_PATTERNS: {
    CATEGORIES: ['api:categories:*'],
    TAGS: ['api:tags:*'],
    PROMPTS: ['api:prompts:*'],
  }
} as const;

import { create } from 'zustand'
import type { Prompt, Category, Tag } from './api'

interface PromptStore {
  // 数据状态
  prompts: Prompt[]
  categories: Category[]
  tags: Tag[]
  
  // UI状态
  selectedCategory: string | null
  selectedTags: string[]
  searchQuery: string
  isLoading: boolean

  // 🚀 批量操作状态
  selectedPromptIds: string[]
  isBatchMode: boolean
  
  // 模态框状态
  isPromptDialogOpen: boolean
  selectedPrompt: Prompt | null
  isEditMode: boolean
  
  // 侧边栏状态
  isSidebarOpen: boolean
  
  // Actions
  setPrompts: (prompts: Prompt[]) => void
  setCategories: (categories: Category[]) => void
  setTags: (tags: Tag[]) => void
  
  setSelectedCategory: (categoryId: string | null) => void
  setSelectedTags: (tagIds: string[]) => void
  setSearchQuery: (query: string) => void
  setIsLoading: (loading: boolean) => void

  // 🚀 批量操作方法
  togglePromptSelection: (promptId: string) => void
  selectAllPrompts: () => void
  clearSelection: () => void
  setBatchMode: (enabled: boolean) => void
  
  openPromptDialog: (prompt?: Prompt, editMode?: boolean) => void
  closePromptDialog: () => void
  
  toggleSidebar: () => void
  setSidebarOpen: (open: boolean) => void
  
  // 计算属性
  filteredPrompts: () => Prompt[]
}

export const usePromptStore = create<PromptStore>((set, get) => ({
  // 初始状态
  prompts: [],
  categories: [],
  tags: [],
  
  selectedCategory: null,
  selectedTags: [],
  searchQuery: '',
  isLoading: false,

  // 🚀 批量操作初始状态
  selectedPromptIds: [],
  isBatchMode: false,
  
  isPromptDialogOpen: false,
  selectedPrompt: null,
  isEditMode: false,
  
  isSidebarOpen: true,
  
  // Actions
  setPrompts: (prompts) => set({ prompts }),
  setCategories: (categories) => set({ categories }),
  setTags: (tags) => set({ tags }),
  
  setSelectedCategory: (categoryId) => set({ selectedCategory: categoryId }),
  setSelectedTags: (tagIds) => set({ selectedTags: tagIds }),
  setSearchQuery: (query) => set({ searchQuery: query }),
  setIsLoading: (loading) => set({ isLoading: loading }),
  
  openPromptDialog: (prompt, editMode = false) => set({ 
    isPromptDialogOpen: true, 
    selectedPrompt: prompt || null,
    isEditMode: editMode 
  }),
  closePromptDialog: () => set({ 
    isPromptDialogOpen: false, 
    selectedPrompt: null,
    isEditMode: false 
  }),
  
  toggleSidebar: () => set((state) => ({ isSidebarOpen: !state.isSidebarOpen })),
  setSidebarOpen: (open) => set({ isSidebarOpen: open }),

  // 🚀 批量操作方法实现
  togglePromptSelection: (promptId) => set((state) => ({
    selectedPromptIds: state.selectedPromptIds.includes(promptId)
      ? state.selectedPromptIds.filter(id => id !== promptId)
      : [...state.selectedPromptIds, promptId]
  })),

  selectAllPrompts: () => set((state) => ({
    selectedPromptIds: state.filteredPrompts().map(p => p.id)
  })),

  clearSelection: () => set({
    selectedPromptIds: [],
    isBatchMode: false
  }),

  setBatchMode: (enabled) => set({
    isBatchMode: enabled,
    selectedPromptIds: enabled ? get().selectedPromptIds : []
  }),
  
  // 计算属性 - 过滤提示词（增强版全文搜索）
  filteredPrompts: () => {
    const { prompts, selectedCategory, selectedTags, searchQuery } = get()

    return prompts.filter((prompt) => {
      // 分类筛选
      if (selectedCategory && prompt.categoryId !== selectedCategory) {
        return false
      }

      // 标签筛选
      if (selectedTags.length > 0) {
        const promptTagIds = prompt.tags?.map(tag => tag.id) || []
        const hasSelectedTag = selectedTags.some(tagId => promptTagIds.includes(tagId))
        if (!hasSelectedTag) {
          return false
        }
      }

      // 🚀 增强版全文搜索筛选
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase()

        // 基础字段搜索
        const matchesTitle = prompt.title.toLowerCase().includes(query)
        const matchesContent = prompt.content.toLowerCase().includes(query)
        const matchesDescription = prompt.description?.toLowerCase().includes(query)

        // 关联数据搜索
        const matchesCategory = prompt.category?.name.toLowerCase().includes(query)
        const matchesTags = prompt.tags?.some(tag =>
          tag.name.toLowerCase().includes(query)
        )

        // 如果没有任何匹配，过滤掉
        if (!matchesTitle && !matchesContent && !matchesDescription && !matchesCategory && !matchesTags) {
          return false
        }
      }

      return true
    })
  }
}))

// 分类相关的 store
interface CategoryStore {
  selectedCategoryForEdit: Category | null
  isCategoryDialogOpen: boolean
  
  openCategoryDialog: (category?: Category) => void
  closeCategoryDialog: () => void
}

export const useCategoryStore = create<CategoryStore>((set) => ({
  selectedCategoryForEdit: null,
  isCategoryDialogOpen: false,
  
  openCategoryDialog: (category) => set({ 
    isCategoryDialogOpen: true, 
    selectedCategoryForEdit: category || null 
  }),
  closeCategoryDialog: () => set({ 
    isCategoryDialogOpen: false, 
    selectedCategoryForEdit: null 
  })
}))

// 标签相关的 store
interface TagStore {
  selectedTagForEdit: Tag | null
  isTagDialogOpen: boolean
  
  openTagDialog: (tag?: Tag) => void
  closeTagDialog: () => void
}

export const useTagStore = create<TagStore>((set) => ({
  selectedTagForEdit: null,
  isTagDialogOpen: false,
  
  openTagDialog: (tag) => set({ 
    isTagDialogOpen: true, 
    selectedTagForEdit: tag || null 
  }),
  closeTagDialog: () => set({ 
    isTagDialogOpen: false, 
    selectedTagForEdit: null 
  })
}))

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/lib/store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport type { Prompt, Category, Tag } from './api'\n\ninterface PromptStore {\n  // 数据状态\n  prompts: Prompt[]\n  categories: Category[]\n  tags: Tag[]\n  \n  // UI状态\n  selectedCategory: string | null\n  selectedTags: string[]\n  searchQuery: string\n  isLoading: boolean\n\n  // 🚀 批量操作状态\n  selectedPromptIds: string[]\n  isBatchMode: boolean\n  \n  // 模态框状态\n  isPromptDialogOpen: boolean\n  selectedPrompt: Prompt | null\n  isEditMode: boolean\n  \n  // 侧边栏状态\n  isSidebarOpen: boolean\n  \n  // Actions\n  setPrompts: (prompts: Prompt[]) => void\n  setCategories: (categories: Category[]) => void\n  setTags: (tags: Tag[]) => void\n  \n  setSelectedCategory: (categoryId: string | null) => void\n  setSelectedTags: (tagIds: string[]) => void\n  setSearchQuery: (query: string) => void\n  setIsLoading: (loading: boolean) => void\n\n  // 🚀 批量操作方法\n  togglePromptSelection: (promptId: string) => void\n  selectAllPrompts: () => void\n  clearSelection: () => void\n  setBatchMode: (enabled: boolean) => void\n  \n  openPromptDialog: (prompt?: Prompt, editMode?: boolean) => void\n  closePromptDialog: () => void\n  \n  toggleSidebar: () => void\n  setSidebarOpen: (open: boolean) => void\n  \n  // 计算属性\n  filteredPrompts: () => Prompt[]\n}\n\nexport const usePromptStore = create<PromptStore>((set, get) => ({\n  // 初始状态\n  prompts: [],\n  categories: [],\n  tags: [],\n  \n  selectedCategory: null,\n  selectedTags: [],\n  searchQuery: '',\n  isLoading: false,\n\n  // 🚀 批量操作初始状态\n  selectedPromptIds: [],\n  isBatchMode: false,\n  \n  isPromptDialogOpen: false,\n  selectedPrompt: null,\n  isEditMode: false,\n  \n  isSidebarOpen: true,\n  \n  // Actions\n  setPrompts: (prompts) => set({ prompts }),\n  setCategories: (categories) => set({ categories }),\n  setTags: (tags) => set({ tags }),\n  \n  setSelectedCategory: (categoryId) => set({ selectedCategory: categoryId }),\n  setSelectedTags: (tagIds) => set({ selectedTags: tagIds }),\n  setSearchQuery: (query) => set({ searchQuery: query }),\n  setIsLoading: (loading) => set({ isLoading: loading }),\n  \n  openPromptDialog: (prompt, editMode = false) => set({ \n    isPromptDialogOpen: true, \n    selectedPrompt: prompt || null,\n    isEditMode: editMode \n  }),\n  closePromptDialog: () => set({ \n    isPromptDialogOpen: false, \n    selectedPrompt: null,\n    isEditMode: false \n  }),\n  \n  toggleSidebar: () => set((state) => ({ isSidebarOpen: !state.isSidebarOpen })),\n  setSidebarOpen: (open) => set({ isSidebarOpen: open }),\n\n  // 🚀 批量操作方法实现\n  togglePromptSelection: (promptId) => set((state) => ({\n    selectedPromptIds: state.selectedPromptIds.includes(promptId)\n      ? state.selectedPromptIds.filter(id => id !== promptId)\n      : [...state.selectedPromptIds, promptId]\n  })),\n\n  selectAllPrompts: () => set((state) => ({\n    selectedPromptIds: state.filteredPrompts().map(p => p.id)\n  })),\n\n  clearSelection: () => set({\n    selectedPromptIds: [],\n    isBatchMode: false\n  }),\n\n  setBatchMode: (enabled) => set({\n    isBatchMode: enabled,\n    selectedPromptIds: enabled ? get().selectedPromptIds : []\n  }),\n  \n  // 计算属性 - 过滤提示词（增强版全文搜索）\n  filteredPrompts: () => {\n    const { prompts, selectedCategory, selectedTags, searchQuery } = get()\n\n    return prompts.filter((prompt) => {\n      // 分类筛选\n      if (selectedCategory && prompt.categoryId !== selectedCategory) {\n        return false\n      }\n\n      // 标签筛选\n      if (selectedTags.length > 0) {\n        const promptTagIds = prompt.tags?.map(tag => tag.id) || []\n        const hasSelectedTag = selectedTags.some(tagId => promptTagIds.includes(tagId))\n        if (!hasSelectedTag) {\n          return false\n        }\n      }\n\n      // 🚀 增强版全文搜索筛选\n      if (searchQuery.trim()) {\n        const query = searchQuery.toLowerCase()\n\n        // 基础字段搜索\n        const matchesTitle = prompt.title.toLowerCase().includes(query)\n        const matchesContent = prompt.content.toLowerCase().includes(query)\n        const matchesDescription = prompt.description?.toLowerCase().includes(query)\n\n        // 关联数据搜索\n        const matchesCategory = prompt.category?.name.toLowerCase().includes(query)\n        const matchesTags = prompt.tags?.some(tag =>\n          tag.name.toLowerCase().includes(query)\n        )\n\n        // 如果没有任何匹配，过滤掉\n        if (!matchesTitle && !matchesContent && !matchesDescription && !matchesCategory && !matchesTags) {\n          return false\n        }\n      }\n\n      return true\n    })\n  }\n}))\n\n// 分类相关的 store\ninterface CategoryStore {\n  selectedCategoryForEdit: Category | null\n  isCategoryDialogOpen: boolean\n  \n  openCategoryDialog: (category?: Category) => void\n  closeCategoryDialog: () => void\n}\n\nexport const useCategoryStore = create<CategoryStore>((set) => ({\n  selectedCategoryForEdit: null,\n  isCategoryDialogOpen: false,\n  \n  openCategoryDialog: (category) => set({ \n    isCategoryDialogOpen: true, \n    selectedCategoryForEdit: category || null \n  }),\n  closeCategoryDialog: () => set({ \n    isCategoryDialogOpen: false, \n    selectedCategoryForEdit: null \n  })\n}))\n\n// 标签相关的 store\ninterface TagStore {\n  selectedTagForEdit: Tag | null\n  isTagDialogOpen: boolean\n  \n  openTagDialog: (tag?: Tag) => void\n  closeTagDialog: () => void\n}\n\nexport const useTagStore = create<TagStore>((set) => ({\n  selectedTagForEdit: null,\n  isTagDialogOpen: false,\n  \n  openTagDialog: (tag) => set({ \n    isTagDialogOpen: true, \n    selectedTagForEdit: tag || null \n  }),\n  closeTagDialog: () => set({ \n    isTagDialogOpen: false, \n    selectedTagForEdit: null \n  })\n}))\n"], "names": [], "mappings": ";;;;;AAAA;;AAqDO,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAe,CAAC,KAAK,MAAQ,CAAC;QAC/D,OAAO;QACP,SAAS,EAAE;QACX,YAAY,EAAE;QACd,MAAM,EAAE;QAER,kBAAkB;QAClB,cAAc,EAAE;QAChB,aAAa;QACb,WAAW;QAEX,cAAc;QACd,mBAAmB,EAAE;QACrB,aAAa;QAEb,oBAAoB;QACpB,gBAAgB;QAChB,YAAY;QAEZ,eAAe;QAEf,UAAU;QACV,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QACvC,eAAe,CAAC,aAAe,IAAI;gBAAE;YAAW;QAChD,SAAS,CAAC,OAAS,IAAI;gBAAE;YAAK;QAE9B,qBAAqB,CAAC,aAAe,IAAI;gBAAE,kBAAkB;YAAW;QACxE,iBAAiB,CAAC,SAAW,IAAI;gBAAE,cAAc;YAAO;QACxD,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;QACpD,cAAc,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAEpD,kBAAkB,SAAC;gBAAQ,4EAAW;mBAAU,IAAI;gBAClD,oBAAoB;gBACpB,gBAAgB,UAAU;gBAC1B,YAAY;YACd;;QACA,mBAAmB,IAAM,IAAI;gBAC3B,oBAAoB;gBACpB,gBAAgB;gBAChB,YAAY;YACd;QAEA,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,eAAe,CAAC,MAAM,aAAa;gBAAC,CAAC;QAC5E,gBAAgB,CAAC,OAAS,IAAI;gBAAE,eAAe;YAAK;QAEpD,cAAc;QACd,uBAAuB,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBACnD,mBAAmB,MAAM,iBAAiB,CAAC,QAAQ,CAAC,YAChD,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,YAC5C;2BAAI,MAAM,iBAAiB;wBAAE;qBAAS;gBAC5C,CAAC;QAED,kBAAkB,IAAM,IAAI,CAAC,QAAU,CAAC;oBACtC,mBAAmB,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;gBAC1D,CAAC;QAED,gBAAgB,IAAM,IAAI;gBACxB,mBAAmB,EAAE;gBACrB,aAAa;YACf;QAEA,cAAc,CAAC,UAAY,IAAI;gBAC7B,aAAa;gBACb,mBAAmB,UAAU,MAAM,iBAAiB,GAAG,EAAE;YAC3D;QAEA,wBAAwB;QACxB,iBAAiB;YACf,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG;YAEjE,OAAO,QAAQ,MAAM,CAAC,CAAC;gBACrB,OAAO;gBACP,IAAI,oBAAoB,OAAO,UAAU,KAAK,kBAAkB;oBAC9D,OAAO;gBACT;gBAEA,OAAO;gBACP,IAAI,aAAa,MAAM,GAAG,GAAG;wBACN;oBAArB,MAAM,eAAe,EAAA,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE,MAAK,EAAE;oBAC1D,MAAM,iBAAiB,aAAa,IAAI,CAAC,CAAA,QAAS,aAAa,QAAQ,CAAC;oBACxE,IAAI,CAAC,gBAAgB;wBACnB,OAAO;oBACT;gBACF;gBAEA,eAAe;gBACf,IAAI,YAAY,IAAI,IAAI;wBAMK,qBAGH,kBACJ;oBATpB,MAAM,QAAQ,YAAY,WAAW;oBAErC,SAAS;oBACT,MAAM,eAAe,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;oBACzD,MAAM,iBAAiB,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAC7D,MAAM,sBAAqB,sBAAA,OAAO,WAAW,cAAlB,0CAAA,oBAAoB,WAAW,GAAG,QAAQ,CAAC;oBAEtE,SAAS;oBACT,MAAM,mBAAkB,mBAAA,OAAO,QAAQ,cAAf,uCAAA,iBAAiB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;oBACrE,MAAM,eAAc,gBAAA,OAAO,IAAI,cAAX,oCAAA,cAAa,IAAI,CAAC,CAAA,MACpC,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAGlC,eAAe;oBACf,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,aAAa;wBAC/F,OAAO;oBACT;gBACF;gBAEA,OAAO;YACT;QACF;IACF,CAAC;AAWM,MAAM,mBAAmB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,MAAQ,CAAC;QAC9D,yBAAyB;QACzB,sBAAsB;QAEtB,oBAAoB,CAAC,WAAa,IAAI;gBACpC,sBAAsB;gBACtB,yBAAyB,YAAY;YACvC;QACA,qBAAqB,IAAM,IAAI;gBAC7B,sBAAsB;gBACtB,yBAAyB;YAC3B;IACF,CAAC;AAWM,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAY,CAAC,MAAQ,CAAC;QACpD,oBAAoB;QACpB,iBAAiB;QAEjB,eAAe,CAAC,MAAQ,IAAI;gBAC1B,iBAAiB;gBACjB,oBAAoB,OAAO;YAC7B;QACA,gBAAgB,IAAM,IAAI;gBACxB,iBAAiB;gBACjB,oBAAoB;YACtB;IACF,CAAC", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/lib/api.ts"], "sourcesContent": ["// 客户端 API 调用层 - 替代直接的数据库调用\n\nexport type Prompt = {\n  id: string\n  title: string\n  content: string\n  description?: string\n  categoryId?: string\n  usageCount: number\n  // 🚀 新增置顶相关字段\n  isPinned: boolean\n  pinnedAt?: string | null\n  createdAt: string\n  updatedAt: string\n  category?: Category | null\n  tags: Tag[]\n}\n\nexport type Category = {\n  id: string\n  name: string\n  color: string\n  icon: string\n  parentId?: string\n  sortOrder: number\n  createdAt: string\n}\n\nexport type Tag = {\n  id: string\n  name: string\n  color: string\n  createdAt: string\n}\n\n// 提示词 API\nexport const promptsApi = {\n  // 获取所有提示词\n  async getAll(): Promise<Prompt[]> {\n    const response = await fetch('/api/prompts')\n    if (!response.ok) {\n      throw new Error('获取提示词失败')\n    }\n    return response.json()\n  },\n\n  // 根据 ID 获取提示词\n  async getById(id: string): Promise<Prompt | null> {\n    const response = await fetch(`/api/prompts/${id}`)\n    if (response.status === 404) {\n      return null\n    }\n    if (!response.ok) {\n      throw new Error('获取提示词失败')\n    }\n    return response.json()\n  },\n\n  // 创建提示词\n  async create(data: {\n    title: string\n    content: string\n    description?: string\n    categoryId?: string\n    tagIds?: string[]\n  }): Promise<Prompt> {\n    const response = await fetch('/api/prompts', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n    if (!response.ok) {\n      throw new Error('创建提示词失败')\n    }\n    return response.json()\n  },\n\n  // 更新提示词\n  async update(id: string, data: {\n    title?: string\n    content?: string\n    description?: string\n    categoryId?: string\n    tagIds?: string[]\n  }): Promise<Prompt> {\n    const response = await fetch(`/api/prompts/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n    if (!response.ok) {\n      throw new Error('更新提示词失败')\n    }\n    return response.json()\n  },\n\n  // 删除提示词\n  async delete(id: string): Promise<void> {\n    const response = await fetch(`/api/prompts/${id}`, {\n      method: 'DELETE',\n    })\n    if (!response.ok) {\n      throw new Error('删除提示词失败')\n    }\n  },\n\n  // 增加使用次数\n  async incrementUsage(id: string): Promise<void> {\n    const response = await fetch(`/api/prompts/${id}/usage`, {\n      method: 'POST',\n    })\n    if (!response.ok) {\n      throw new Error('增加使用次数失败')\n    }\n  },\n\n  // 按分类筛选\n  async getByCategory(categoryId: string): Promise<Prompt[]> {\n    const response = await fetch(`/api/prompts?categoryId=${categoryId}`)\n    if (!response.ok) {\n      throw new Error('获取提示词失败')\n    }\n    return response.json()\n  },\n\n  // 🚀 新增功能：增加使用次数\n  async incrementUsage(id: string): Promise<void> {\n    const response = await fetch(`/api/prompts/${id}/usage`, {\n      method: 'POST'\n    })\n    if (!response.ok) {\n      throw new Error('更新使用次数失败')\n    }\n  },\n\n  // 🚀 新增功能：全文搜索\n  async search(keyword: string): Promise<Prompt[]> {\n    const response = await fetch(`/api/prompts/search?q=${encodeURIComponent(keyword)}`)\n    if (!response.ok) {\n      throw new Error('搜索失败')\n    }\n    const data = await response.json()\n    return data.prompts || []\n  },\n\n  // 🚀 新增功能：置顶/取消置顶\n  async togglePin(id: string): Promise<Prompt> {\n    const response = await fetch(`/api/prompts/${id}/pin`, {\n      method: 'POST'\n    })\n    if (!response.ok) {\n      throw new Error('切换置顶状态失败')\n    }\n    const data = await response.json()\n    return data.prompt\n  },\n\n  // 🚀 新增功能：批量删除\n  async deleteMany(ids: string[]): Promise<void> {\n    const response = await fetch('/api/prompts/batch', {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({ ids })\n    })\n    if (!response.ok) {\n      throw new Error('批量删除失败')\n    }\n  },\n\n  // 🚀 新增功能：批量移动分类\n  async updateCategory(ids: string[], categoryId: string | null): Promise<void> {\n    const response = await fetch('/api/prompts/batch', {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        ids,\n        action: 'move_category',\n        data: { categoryId }\n      })\n    })\n    if (!response.ok) {\n      throw new Error('批量移动分类失败')\n    }\n  }\n}\n\n// 分类 API\nexport const categoriesApi = {\n  // 获取所有分类\n  async getAll(): Promise<Category[]> {\n    const response = await fetch('/api/categories')\n    if (!response.ok) {\n      throw new Error('获取分类失败')\n    }\n    return response.json()\n  },\n\n  // 创建分类\n  async create(data: {\n    name: string\n    color?: string\n    icon?: string\n    parentId?: string\n    sortOrder?: number\n  }): Promise<Category> {\n    const response = await fetch('/api/categories', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n    if (!response.ok) {\n      throw new Error('创建分类失败')\n    }\n    return response.json()\n  },\n\n  // 更新分类\n  async update(id: string, data: {\n    name?: string\n    color?: string\n    icon?: string\n    parentId?: string\n    sortOrder?: number\n  }): Promise<Category> {\n    const response = await fetch(`/api/categories/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n    if (!response.ok) {\n      throw new Error('更新分类失败')\n    }\n    return response.json()\n  },\n\n  // 删除分类\n  async delete(id: string): Promise<void> {\n    const response = await fetch(`/api/categories/${id}`, {\n      method: 'DELETE',\n    })\n    if (!response.ok) {\n      throw new Error('删除分类失败')\n    }\n  }\n}\n\n// 标签 API\nexport const tagsApi = {\n  // 获取所有标签\n  async getAll(): Promise<Tag[]> {\n    const response = await fetch('/api/tags')\n    if (!response.ok) {\n      throw new Error('获取标签失败')\n    }\n    return response.json()\n  },\n\n  // 创建标签\n  async create(data: {\n    name: string\n    color?: string\n  }): Promise<Tag> {\n    const response = await fetch('/api/tags', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n    if (!response.ok) {\n      throw new Error('创建标签失败')\n    }\n    return response.json()\n  },\n\n  // 更新标签\n  async update(id: string, data: {\n    name?: string\n    color?: string\n  }): Promise<Tag> {\n    const response = await fetch(`/api/tags/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n    if (!response.ok) {\n      throw new Error('更新标签失败')\n    }\n    return response.json()\n  },\n\n  // 删除标签\n  async delete(id: string): Promise<void> {\n    const response = await fetch(`/api/tags/${id}`, {\n      method: 'DELETE',\n    })\n    if (!response.ok) {\n      throw new Error('删除标签失败')\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;AAoCpB,MAAM,aAAa;IACxB,UAAU;IACV,MAAM;QACJ,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,MAAM,SAAQ,EAAU;QACtB,MAAM,WAAW,MAAM,MAAM,AAAC,gBAAkB,OAAH;QAC7C,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;IACR,MAAM,QAAO,IAMZ;QACC,MAAM,WAAW,MAAM,MAAM,gBAAgB;YAC3C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;IACR,MAAM,QAAO,EAAU,EAAE,IAMxB;QACC,MAAM,WAAW,MAAM,MAAM,AAAC,gBAAkB,OAAH,KAAM;YACjD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;IACR,MAAM,QAAO,EAAU;QACrB,MAAM,WAAW,MAAM,MAAM,AAAC,gBAAkB,OAAH,KAAM;YACjD,QAAQ;QACV;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,SAAS;IACT,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,MAAM,AAAC,gBAAkB,OAAH,IAAG,WAAS;YACvD,QAAQ;QACV;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,QAAQ;IACR,MAAM,eAAc,UAAkB;QACpC,MAAM,WAAW,MAAM,MAAM,AAAC,2BAAqC,OAAX;QACxD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,MAAM,AAAC,gBAAkB,OAAH,IAAG,WAAS;YACvD,QAAQ;QACV;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,eAAe;IACf,MAAM,QAAO,OAAe;QAC1B,MAAM,WAAW,MAAM,MAAM,AAAC,yBAAoD,OAA5B,mBAAmB;QACzE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,OAAO,IAAI,EAAE;IAC3B;IAEA,kBAAkB;IAClB,MAAM,WAAU,EAAU;QACxB,MAAM,WAAW,MAAM,MAAM,AAAC,gBAAkB,OAAH,IAAG,SAAO;YACrD,QAAQ;QACV;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,MAAM;IACpB;IAEA,eAAe;IACf,MAAM,YAAW,GAAa;QAC5B,MAAM,WAAW,MAAM,MAAM,sBAAsB;YACjD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAI;QAC7B;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAe,GAAa,EAAE,UAAyB;QAC3D,MAAM,WAAW,MAAM,MAAM,sBAAsB;YACjD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA,QAAQ;gBACR,MAAM;oBAAE;gBAAW;YACrB;QACF;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,SAAS;IACT,MAAM;QACJ,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QAAO,IAMZ;QACC,MAAM,WAAW,MAAM,MAAM,mBAAmB;YAC9C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QAAO,EAAU,EAAE,IAMxB;QACC,MAAM,WAAW,MAAM,MAAM,AAAC,mBAAqB,OAAH,KAAM;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QAAO,EAAU;QACrB,MAAM,WAAW,MAAM,MAAM,AAAC,mBAAqB,OAAH,KAAM;YACpD,QAAQ;QACV;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM,UAAU;IACrB,SAAS;IACT,MAAM;QACJ,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QAAO,IAGZ;QACC,MAAM,WAAW,MAAM,MAAM,aAAa;YACxC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QAAO,EAAU,EAAE,IAGxB;QACC,MAAM,WAAW,MAAM,MAAM,AAAC,aAAe,OAAH,KAAM;YAC9C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,MAAM,QAAO,EAAU;QACrB,MAAM,WAAW,MAAM,MAAM,AAAC,aAAe,OAAH,KAAM;YAC9C,QAAQ;QACV;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,6JAAA,CAAA,WAAc,CAAsB;IAEpE,6JAAA,CAAA,YAAe;iCAAC;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,AAAC,eAAoC,OAAtB,oBAAoB,GAAE;YACnE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-lg border-2 border-primary hover:bg-primary/90 hover:border-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border-2 border-slate-400 bg-white shadow-md hover:bg-accent hover:text-accent-foreground hover:border-primary\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground bg-white border-slate-400 flex h-9 w-full min-w-0 rounded-md border-2 px-3 py-1 text-base shadow-md transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-primary focus-visible:ring-primary/30 focus-visible:ring-4\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kbACA,mFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,KAKoC;QALpC,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD,GALpC;IAMjB,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,KAA8D;QAA9D,EAAE,GAAG,OAAyD,GAA9D;IACb,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,KAEgC;QAFhC,EAClB,GAAG,OAC+C,GAFhC;IAGlB,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,KAGgC;QAHhC,EACpB,SAAS,EACT,GAAG,OACiD,GAHhC;IAIpB,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,KAOrB;QAPqB,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ,GAPqB;IAQpB,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAGgC;QAHhC,EAClB,SAAS,EACT,GAAG,OAC+C,GAHhC;IAIlB,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 837, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,KAEoC;QAFpC,EACf,GAAG,OACgD,GAFpC;IAGf,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,KAEgC;QAFhC,EACtB,GAAG,OACmD,GAFhC;IAGtB,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,KAKgC;QALhC,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD,GALhC;IAMtB,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 938, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, VariantProps } from \"class-variance-authority\"\nimport { PanelLeftIcon } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport {\n  Sheet,\n  SheetContent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from \"@/components/ui/sheet\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  Toolt<PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContextProps = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nfunction SidebarProvider({\n  defaultOpen = true,\n  open: openProp,\n  onOpenChange: setOpenProp,\n  className,\n  style,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  defaultOpen?: boolean\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}) {\n  const isMobile = useIsMobile()\n  const [openMobile, setOpenMobile] = React.useState(false)\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen)\n  const open = openProp ?? _open\n  const setOpen = React.useCallback(\n    (value: boolean | ((value: boolean) => boolean)) => {\n      const openState = typeof value === \"function\" ? value(open) : value\n      if (setOpenProp) {\n        setOpenProp(openState)\n      } else {\n        _setOpen(openState)\n      }\n\n      // This sets the cookie to keep the sidebar state.\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n    },\n    [setOpenProp, open]\n  )\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\n  }, [isMobile, setOpen, setOpenMobile])\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n        (event.metaKey || event.ctrlKey)\n      ) {\n        event.preventDefault()\n        toggleSidebar()\n      }\n    }\n\n    window.addEventListener(\"keydown\", handleKeyDown)\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\n  }, [toggleSidebar])\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? \"expanded\" : \"collapsed\"\n\n  const contextValue = React.useMemo<SidebarContextProps>(\n    () => ({\n      state,\n      open,\n      setOpen,\n      isMobile,\n      openMobile,\n      setOpenMobile,\n      toggleSidebar,\n    }),\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n  )\n\n  return (\n    <SidebarContext.Provider value={contextValue}>\n      <TooltipProvider delayDuration={0}>\n        <div\n          data-slot=\"sidebar-wrapper\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH,\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n              ...style,\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\n            className\n          )}\n          {...props}\n        >\n          {children}\n        </div>\n      </TooltipProvider>\n    </SidebarContext.Provider>\n  )\n}\n\nfunction Sidebar({\n  side = \"left\",\n  variant = \"sidebar\",\n  collapsible = \"offcanvas\",\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  side?: \"left\" | \"right\"\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n}) {\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n  if (collapsible === \"none\") {\n    return (\n      <div\n        data-slot=\"sidebar\"\n        className={cn(\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n\n  if (isMobile) {\n    return (\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n        <SheetContent\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar\"\n          data-mobile=\"true\"\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n            } as React.CSSProperties\n          }\n          side={side}\n        >\n          <SheetHeader className=\"sr-only\">\n            <SheetTitle>Sidebar</SheetTitle>\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n          </SheetHeader>\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\n        </SheetContent>\n      </Sheet>\n    )\n  }\n\n  return (\n    <div\n      className=\"group peer text-sidebar-foreground hidden md:block\"\n      data-state={state}\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n      data-variant={variant}\n      data-side={side}\n      data-slot=\"sidebar\"\n    >\n      {/* This is what handles the sidebar gap on desktop */}\n      <div\n        data-slot=\"sidebar-gap\"\n        className={cn(\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\n          \"group-data-[collapsible=offcanvas]:w-0\",\n          \"group-data-[side=right]:rotate-180\",\n          variant === \"floating\" || variant === \"inset\"\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\n        )}\n      />\n      <div\n        data-slot=\"sidebar-container\"\n        className={cn(\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\n          side === \"left\"\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n          // Adjust the padding for floating and inset variants.\n          variant === \"floating\" || variant === \"inset\"\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n          className\n        )}\n        {...props}\n      >\n        <div\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar-inner\"\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\n        >\n          {children}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nfunction SidebarTrigger({\n  className,\n  onClick,\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      data-sidebar=\"trigger\"\n      data-slot=\"sidebar-trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"size-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeftIcon />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n}\n\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      data-sidebar=\"rail\"\n      data-slot=\"sidebar-rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\n  return (\n    <main\n      data-slot=\"sidebar-inset\"\n      className={cn(\n        \"bg-background relative flex w-full flex-1 flex-col\",\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof Input>) {\n  return (\n    <Input\n      data-slot=\"sidebar-input\"\n      data-sidebar=\"input\"\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-header\"\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-footer\"\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof Separator>) {\n  return (\n    <Separator\n      data-slot=\"sidebar-separator\"\n      data-sidebar=\"separator\"\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-content\"\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group\"\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupLabel({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-label\"\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupAction({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-action\"\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupContent({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group-content\"\n      data-sidebar=\"group-content\"\n      className={cn(\"w-full text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu\"\n      data-sidebar=\"menu\"\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-item\"\n      data-sidebar=\"menu-item\"\n      className={cn(\"group/menu-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction SidebarMenuButton({\n  asChild = false,\n  isActive = false,\n  variant = \"default\",\n  size = \"default\",\n  tooltip,\n  className,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  isActive?: boolean\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\n  const Comp = asChild ? Slot : \"button\"\n  const { isMobile, state } = useSidebar()\n\n  const button = (\n    <Comp\n      data-slot=\"sidebar-menu-button\"\n      data-sidebar=\"menu-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n      {...props}\n    />\n  )\n\n  if (!tooltip) {\n    return button\n  }\n\n  if (typeof tooltip === \"string\") {\n    tooltip = {\n      children: tooltip,\n    }\n  }\n\n  return (\n    <Tooltip>\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\n      <TooltipContent\n        side=\"right\"\n        align=\"center\"\n        hidden={state !== \"collapsed\" || isMobile}\n        {...tooltip}\n      />\n    </Tooltip>\n  )\n}\n\nfunction SidebarMenuAction({\n  className,\n  asChild = false,\n  showOnHover = false,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  showOnHover?: boolean\n}) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-action\"\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuBadge({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-menu-badge\"\n      data-sidebar=\"menu-badge\"\n      className={cn(\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSkeleton({\n  className,\n  showIcon = false,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  showIcon?: boolean\n}) {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      data-slot=\"sidebar-menu-skeleton\"\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n}\n\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu-sub\"\n      data-sidebar=\"menu-sub\"\n      className={cn(\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubItem({\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-sub-item\"\n      data-sidebar=\"menu-sub-item\"\n      className={cn(\"group/menu-sub-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubButton({\n  asChild = false,\n  size = \"md\",\n  isActive = false,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean\n  size?: \"sm\" | \"md\"\n  isActive?: boolean\n}) {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-sub-button\"\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,6JAAA,CAAA,gBAAmB,CAA6B;AAEvE,SAAS;;IACP,MAAM,UAAU,6JAAA,CAAA,aAAgB,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,KAYxB;QAZwB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ,GAZwB;;IAavB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,WAAc,CAAC;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,6JAAA,CAAA,WAAc,CAAC;IACzC,MAAM,OAAO,qBAAA,sBAAA,WAAY;IACzB,MAAM,UAAU,6JAAA,CAAA,cAAiB;gDAC/B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,AAAC,GAAyB,OAAvB,qBAAoB,KAAiC,OAA9B,WAAU,sBAA2C,OAAvB;QAC5E;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,6JAAA,CAAA,cAAiB;sDAAC;YACtC,OAAO,WAAW;8DAAc,CAAC,OAAS,CAAC;+DAAQ;8DAAQ,CAAC,OAAS,CAAC;;QACxE;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,6JAAA,CAAA,YAAe;qCAAC;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,6JAAA,CAAA,UAAa;iDAChC,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,sIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IAhGS;;QAaU,gIAAA,CAAA,cAAW;;;KAbrB;AAkGT,SAAS,QAAQ,KAWhB;QAXgB,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ,GAXgB;;IAYf,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,oIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,oIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,oIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6LAAC,oIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,KAIc;QAJd,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC,GAJd;;IAKtB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,oBAAA,8BAAA,QAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IAxBS;;QAKmB;;;MALnB;AA0BT,SAAS,YAAY,KAAuD;QAAvD,EAAE,SAAS,EAAE,GAAG,OAAuC,GAAvD;;IACnB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,KAAqD;QAArD,EAAE,SAAS,EAAE,GAAG,OAAqC,GAArD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,KAGe;QAHf,EACpB,SAAS,EACT,GAAG,OACgC,GAHf;IAIpB,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,cAAc,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACrB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACrB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,KAGe;QAHf,EACxB,SAAS,EACT,GAAG,OACoC,GAHf;IAIxB,qBACE,6LAAC,wIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,eAAe,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,KAI2B;QAJ3B,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD,GAJ3B;IAKzB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,KAI6B;QAJ7B,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD,GAJ7B;IAK1B,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,KAGC;QAHD,EAC3B,SAAS,EACT,GAAG,OACyB,GAHD;IAI3B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,YAAY,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,KAYuB;QAZvB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C,GAZvB;;IAazB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,sIAAA,CAAA,UAAO;;0BACN,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,sIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;IAhDS;;QAcqB;;;OAdrB;AAkDT,SAAS,kBAAkB,KAQ1B;QAR0B,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ,GAR0B;IASzB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS;AAgCT,SAAS,iBAAiB,KAGI;QAHJ,EACxB,SAAS,EACT,GAAG,OACyB,GAHJ;IAIxB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OApBS;AAsBT,SAAS,oBAAoB,KAM5B;QAN4B,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ,GAN4B;;IAO3B,kCAAkC;IAClC,MAAM,QAAQ,6JAAA,CAAA,UAAa;8CAAC;YAC1B,OAAO,AAAC,GAAsC,OAApC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,IAAG;QAChD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IApCS;OAAA;AAsCT,SAAS,eAAe,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,KAGC;QAHD,EAC1B,SAAS,EACT,GAAG,OACwB,GAHD;IAI1B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,qBAAqB,KAU7B;QAV6B,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ,GAV6B;IAW5B,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS", "debugId": null}}, {"offset": {"line": 1713, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1766, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,KAEoC;QAFpC,EACpB,GAAG,OACqD,GAFpC;IAGpB,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,KAEgC;QAFhC,EAC3B,GAAG,OACwD,GAFhC;IAG3B,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,KAIgC;QAJhC,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD,GAJhC;IAK3B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,KAQzB;QARyB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ,GARyB;IASxB,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,KAKgC;QALhC,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D,GALhC;IAMhC,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,KAEgC;QAFhC,EAC9B,GAAG,OAC2D,GAFhC;IAG9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,KAM1B;QAN0B,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ,GAN0B;IAOzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,KAGC;QAHD,EAC5B,SAAS,EACT,GAAG,OAC0B,GAHD;IAI5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,KAEgC;QAFhC,EACvB,GAAG,OACoD,GAFhC;IAGvB,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,KAO/B;QAP+B,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ,GAP+B;IAQ9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 2077, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/lib/icons.tsx"], "sourcesContent": ["import { \n  Folder, \n  Briefcase, \n  BookOpen, \n  Lightbulb, \n  Code, \n  Home, \n  Heart, \n  Star, \n  Tag, \n  Settings,\n  FolderOpen\n} from 'lucide-react'\n\n// 图标映射表\nexport const ICON_MAP = {\n  'folder': Folder,\n  'briefcase': Briefcase,\n  'book-open': BookOpen,\n  'lightbulb': Lightbulb,\n  'code': Code,\n  'home': Home,\n  'heart': Heart,\n  'star': Star,\n  'tag': Tag,\n  'settings': Settings,\n} as const\n\n// 图标配置\nexport const PRESET_ICONS = [\n  { value: \"folder\", label: \"文件夹\", icon: \"📁\" },\n  { value: \"briefcase\", label: \"工作\", icon: \"💼\" },\n  { value: \"book-open\", label: \"学习\", icon: \"📖\" },\n  { value: \"lightbulb\", label: \"创意\", icon: \"💡\" },\n  { value: \"code\", label: \"编程\", icon: \"💻\" },\n  { value: \"home\", label: \"生活\", icon: \"🏠\" },\n  { value: \"heart\", label: \"收藏\", icon: \"❤️\" },\n  { value: \"star\", label: \"重要\", icon: \"⭐\" },\n  { value: \"tag\", label: \"标签\", icon: \"🏷️\" },\n  { value: \"settings\", label: \"设置\", icon: \"⚙️\" },\n] as const\n\n// 获取图标组件\nexport function getIconComponent(iconName: string, isOpen = false) {\n  // 如果是选中状态且是文件夹图标，显示打开的文件夹\n  if (isOpen && iconName === 'folder') {\n    return FolderOpen\n  }\n  \n  // 返回对应的图标组件，如果找不到则返回默认的文件夹图标\n  return ICON_MAP[iconName as keyof typeof ICON_MAP] || Folder\n}\n\n// 获取图标的emoji表示\nexport function getIconEmoji(iconName: string) {\n  const iconConfig = PRESET_ICONS.find(icon => icon.value === iconName)\n  return iconConfig?.icon || \"📁\"\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAeO,MAAM,WAAW;IACtB,UAAU,yMAAA,CAAA,SAAM;IAChB,aAAa,+MAAA,CAAA,YAAS;IACtB,aAAa,iNAAA,CAAA,WAAQ;IACrB,aAAa,+MAAA,CAAA,YAAS;IACtB,QAAQ,qMAAA,CAAA,OAAI;IACZ,QAAQ,sMAAA,CAAA,OAAI;IACZ,SAAS,uMAAA,CAAA,QAAK;IACd,QAAQ,qMAAA,CAAA,OAAI;IACZ,OAAO,mMAAA,CAAA,MAAG;IACV,YAAY,6MAAA,CAAA,WAAQ;AACtB;AAGO,MAAM,eAAe;IAC1B;QAAE,OAAO;QAAU,OAAO;QAAO,MAAM;IAAK;IAC5C;QAAE,OAAO;QAAa,OAAO;QAAM,MAAM;IAAK;IAC9C;QAAE,OAAO;QAAa,OAAO;QAAM,MAAM;IAAK;IAC9C;QAAE,OAAO;QAAa,OAAO;QAAM,MAAM;IAAK;IAC9C;QAAE,OAAO;QAAQ,OAAO;QAAM,MAAM;IAAK;IACzC;QAAE,OAAO;QAAQ,OAAO;QAAM,MAAM;IAAK;IACzC;QAAE,OAAO;QAAS,OAAO;QAAM,MAAM;IAAK;IAC1C;QAAE,OAAO;QAAQ,OAAO;QAAM,MAAM;IAAI;IACxC;QAAE,OAAO;QAAO,OAAO;QAAM,MAAM;IAAM;IACzC;QAAE,OAAO;QAAY,OAAO;QAAM,MAAM;IAAK;CAC9C;AAGM,SAAS,iBAAiB,QAAgB;QAAE,SAAA,iEAAS;IAC1D,0BAA0B;IAC1B,IAAI,UAAU,aAAa,UAAU;QACnC,OAAO,qNAAA,CAAA,aAAU;IACnB;IAEA,6BAA6B;IAC7B,OAAO,QAAQ,CAAC,SAAkC,IAAI,yMAAA,CAAA,SAAM;AAC9D;AAGO,SAAS,aAAa,QAAgB;IAC3C,MAAM,aAAa,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;IAC5D,OAAO,CAAA,uBAAA,iCAAA,WAAY,IAAI,KAAI;AAC7B", "debugId": null}}, {"offset": {"line": 2181, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { usePromptStore, useCategoryStore, useTagStore } from \"@/lib/store\";\nimport { categoriesApi, tagsApi, type Tag } from \"@/lib/api\";\nimport {\n  Sidebar,\n  SidebarContent,\n  SidebarHeader,\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarRail,\n} from \"@/components/ui/sidebar\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport {\n  Folder,\n  FolderOpen,\n  Plus,\n  Hash,\n  Sparkles,\n  MoreHorizontal,\n  Edit,\n  Trash2\n} from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\nimport { toast } from \"sonner\";\nimport { useState } from \"react\";\nimport { getIconComponent } from \"@/lib/icons\";\n\nexport function AppSidebar() {\n  const {\n    categories,\n    tags,\n    prompts,\n    selectedCategory,\n    selectedTags,\n    setSelectedCategory,\n    setSelectedTags,\n    setCategories,\n    setTags,\n  } = usePromptStore();\n\n  const { openCategoryDialog } = useCategoryStore();\n  const { openTagDialog } = useTagStore();\n  const [isDeleting, setIsDeleting] = useState<string | null>(null);\n\n  // 计算每个分类的提示词数量\n  const getCategoryCount = (categoryId: string) => {\n    return prompts.filter(prompt => prompt.categoryId === categoryId).length;\n  };\n\n  // 计算每个标签的提示词数量\n  const getTagCount = (tagId: string) => {\n    return prompts.filter(prompt => \n      prompt.tags?.some(tag => tag.id === tagId)\n    ).length;\n  };\n\n  // 处理分类选择\n  const handleCategorySelect = (categoryId: string | null) => {\n    setSelectedCategory(categoryId);\n  };\n\n  // 处理标签选择\n  const handleTagSelect = (tagId: string) => {\n    const newSelectedTags = selectedTags.includes(tagId)\n      ? selectedTags.filter(id => id !== tagId)\n      : [...selectedTags, tagId];\n    setSelectedTags(newSelectedTags);\n  };\n\n  // 编辑分类\n  const handleEditCategory = (category: any) => {\n    openCategoryDialog(category);\n  };\n\n  // 删除分类\n  const handleDeleteCategory = async (categoryId: string) => {\n    if (!confirm(\"确定要删除这个分类吗？删除后该分类下的提示词将变为无分类。\")) {\n      return;\n    }\n\n    try {\n      setIsDeleting(categoryId);\n      await categoriesApi.delete(categoryId);\n\n      // 更新本地状态\n      const updatedCategories = categories.filter(cat => cat.id !== categoryId);\n      setCategories(updatedCategories);\n\n      // 如果当前选中的分类被删除，清除选择\n      if (selectedCategory === categoryId) {\n        setSelectedCategory(null);\n      }\n\n      toast.success(\"分类已删除\");\n    } catch (error) {\n      console.error(\"删除分类失败:\", error);\n      toast.error(\"删除分类失败\");\n    } finally {\n      setIsDeleting(null);\n    }\n  };\n\n  // 编辑标签\n  const handleEditTag = (tag: Tag) => {\n    openTagDialog(tag);\n  };\n\n  // 删除标签\n  const handleDeleteTag = async (tagId: string) => {\n    if (!confirm(\"确定要删除这个标签吗？删除后该标签下的提示词将失去此标签。\")) {\n      return;\n    }\n\n    try {\n      setIsDeleting(tagId);\n      await tagsApi.delete(tagId);\n\n      // 更新本地状态\n      const updatedTags = tags.filter(tag => tag.id !== tagId);\n      setTags(updatedTags);\n\n      // 如果当前选中的标签被删除，清除选择\n      if (selectedTags.includes(tagId)) {\n        setSelectedTags(selectedTags.filter(id => id !== tagId));\n      }\n\n      toast.success(\"标签已删除\");\n    } catch (error) {\n      console.error(\"删除标签失败:\", error);\n      toast.error(\"删除标签失败\");\n    } finally {\n      setIsDeleting(null);\n    }\n  };\n\n  return (\n    <Sidebar className=\"bg-sidebar border-r-2 border-sidebar-border\">\n      <SidebarHeader className=\"border-b-2 border-sidebar-border px-6 py-4\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"p-2 rounded-xl bg-primary shadow-md\">\n            <Sparkles className=\"h-5 w-5 text-white\" />\n          </div>\n          <div>\n            <h1 className=\"text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent\">\n              提示词管理\n            </h1>\n            <p className=\"text-xs text-muted-foreground/70\">智能提示词工具</p>\n          </div>\n        </div>\n      </SidebarHeader>\n\n      <SidebarContent className=\"px-4 py-4 space-y-6\">\n        {/* 全部提示词 */}\n        <SidebarMenu>\n          <SidebarMenuItem>\n            <SidebarMenuButton\n              onClick={() => handleCategorySelect(null)}\n              className={cn(\n                \"w-full justify-between rounded-xl transition-all duration-300\",\n                \"hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10\",\n                \"hover:shadow-md hover:scale-[1.02]\",\n                selectedCategory === null && \"bg-gradient-to-r from-blue-500/15 to-purple-500/15 shadow-md border border-blue-500/20\"\n              )}\n            >\n              <div className=\"flex items-center gap-3\">\n                <div className={cn(\n                  \"p-1.5 rounded-lg transition-all duration-300\",\n                  selectedCategory === null\n                    ? \"bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg\"\n                    : \"bg-muted/50 text-muted-foreground\"\n                )}>\n                  <Folder className=\"h-4 w-4\" />\n                </div>\n                <span className=\"font-medium\">全部提示词</span>\n              </div>\n              <Badge\n                variant=\"secondary\"\n                className={cn(\n                  \"ml-auto font-bold transition-all duration-300\",\n                  selectedCategory === null\n                    ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md\"\n                    : \"bg-muted/70\"\n                )}\n              >\n                {prompts.length}\n              </Badge>\n            </SidebarMenuButton>\n          </SidebarMenuItem>\n        </SidebarMenu>\n\n        {/* 分类 */}\n        <div>\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-sm font-bold text-muted-foreground/80 uppercase tracking-wider\">分类</h3>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => openCategoryDialog()}\n              className={cn(\n                \"h-7 w-7 p-0 rounded-lg transition-all duration-300\",\n                \"hover:bg-gradient-to-r hover:from-green-500/20 hover:to-emerald-500/20\",\n                \"hover:scale-110 hover:shadow-md\"\n              )}\n            >\n              <Plus className=\"h-3.5 w-3.5\" />\n            </Button>\n          </div>\n\n          <SidebarMenu className=\"space-y-2\">\n            {categories.map((category) => (\n              <SidebarMenuItem key={category.id}>\n                <div className=\"flex items-center group\">\n                  <SidebarMenuButton\n                    onClick={() => handleCategorySelect(category.id)}\n                    className={cn(\n                      \"flex-1 justify-between rounded-xl transition-all duration-300\",\n                      \"hover:bg-gradient-to-r hover:shadow-md hover:scale-[1.02]\",\n                      selectedCategory === category.id\n                        ? \"shadow-md border\"\n                        : \"hover:from-gray-500/10 hover:to-gray-600/10\"\n                    )}\n                    style={{\n                      ...(selectedCategory === category.id && {\n                        background: `linear-gradient(135deg, ${category.color}15, ${category.color}25)`,\n                        borderColor: `${category.color}30`,\n                        boxShadow: `0 4px 12px ${category.color}20`\n                      })\n                    }}\n                  >\n                    <div className=\"flex items-center gap-3\">\n                      <div\n                        className={cn(\n                          \"p-1.5 rounded-lg transition-all duration-300\",\n                          selectedCategory === category.id\n                            ? \"shadow-lg text-white\"\n                            : \"bg-muted/50\"\n                        )}\n                        style={{\n                          ...(selectedCategory === category.id && {\n                            background: `linear-gradient(135deg, ${category.color}, ${category.color}dd)`\n                          })\n                        }}\n                      >\n                        {(() => {\n                          const IconComponent = getIconComponent(category.icon, selectedCategory === category.id);\n                          return <IconComponent className=\"h-4 w-4\" style={{ color: selectedCategory === category.id ? 'white' : category.color }} />;\n                        })()}\n                      </div>\n                      <span className=\"truncate font-medium\">{category.name}</span>\n                    </div>\n                    <Badge\n                      variant=\"secondary\"\n                      className={cn(\n                        \"ml-auto font-bold transition-all duration-300\",\n                        selectedCategory === category.id && \"text-white shadow-md\"\n                      )}\n                      style={{\n                        ...(selectedCategory === category.id && {\n                          background: category.color,\n                          color: 'white'\n                        })\n                      }}\n                    >\n                      {getCategoryCount(category.id)}\n                    </Badge>\n                  </SidebarMenuButton>\n\n                  {/* 分类操作菜单 */}\n                  <DropdownMenu>\n                    <DropdownMenuTrigger asChild>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className={cn(\n                          \"h-8 w-8 p-0 rounded-lg transition-all duration-300\",\n                          \"opacity-60 hover:opacity-100\",\n                          \"hover:bg-gradient-to-r hover:from-gray-500/20 hover:to-gray-600/20\",\n                          \"hover:scale-110\"\n                        )}\n                        disabled={isDeleting === category.id}\n                      >\n                        <MoreHorizontal className=\"h-4 w-4\" />\n                      </Button>\n                    </DropdownMenuTrigger>\n                    <DropdownMenuContent align=\"end\" className=\"glass-effect border-border/50\">\n                      <DropdownMenuItem onClick={() => handleEditCategory(category)} className=\"hover:bg-blue-500/10\">\n                        <Edit className=\"h-4 w-4 mr-2\" />\n                        编辑分类\n                      </DropdownMenuItem>\n                      <DropdownMenuSeparator />\n                      <DropdownMenuItem\n                        onClick={() => handleDeleteCategory(category.id)}\n                        className=\"text-destructive focus:text-destructive hover:bg-red-500/10\"\n                        disabled={isDeleting === category.id}\n                      >\n                        <Trash2 className=\"h-4 w-4 mr-2\" />\n                        删除分类\n                      </DropdownMenuItem>\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                </div>\n              </SidebarMenuItem>\n            ))}\n          </SidebarMenu>\n\n          {categories.length === 0 && (\n            <div className=\"text-sm text-muted-foreground/60 text-center py-6 px-4 rounded-xl bg-gradient-to-br from-gray-50/50 to-gray-100/30 dark:from-gray-800/30 dark:to-gray-900/20 border border-dashed border-border/50\">\n              <Folder className=\"h-8 w-8 mx-auto mb-2 text-muted-foreground/40\" />\n              <p>暂无分类</p>\n              <p className=\"text-xs mt-1\">点击上方 + 号创建分类</p>\n            </div>\n          )}\n        </div>\n\n        {/* 标签 */}\n        <div>\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-sm font-bold text-muted-foreground/80 uppercase tracking-wider\">标签</h3>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => openTagDialog()}\n              className={cn(\n                \"h-7 w-7 p-0 rounded-lg transition-all duration-300\",\n                \"hover:bg-gradient-to-r hover:from-purple-500/20 hover:to-pink-500/20\",\n                \"hover:scale-110 hover:shadow-md\"\n              )}\n            >\n              <Plus className=\"h-3.5 w-3.5\" />\n            </Button>\n          </div>\n\n          <SidebarMenu className=\"space-y-2\">\n            {tags.map((tag) => (\n              <SidebarMenuItem key={tag.id}>\n                <div className=\"flex items-center gap-1\">\n                  <SidebarMenuButton\n                    onClick={() => handleTagSelect(tag.id)}\n                    className={cn(\n                      \"flex-1 justify-between rounded-xl transition-all duration-300\",\n                      \"hover:bg-gradient-to-r hover:shadow-md hover:scale-[1.02]\",\n                      selectedTags.includes(tag.id)\n                        ? \"shadow-md border\"\n                        : \"hover:from-gray-500/10 hover:to-gray-600/10\"\n                    )}\n                    style={{\n                      ...(selectedTags.includes(tag.id) && {\n                        background: `linear-gradient(135deg, ${tag.color}15, ${tag.color}25)`,\n                        borderColor: `${tag.color}30`,\n                        boxShadow: `0 4px 12px ${tag.color}20`\n                      })\n                    }}\n                  >\n                    <div className=\"flex items-center gap-3\">\n                      <div\n                        className={cn(\n                          \"p-1.5 rounded-lg transition-all duration-300\",\n                          selectedTags.includes(tag.id)\n                            ? \"shadow-lg text-white\"\n                            : \"bg-muted/50\"\n                        )}\n                        style={{\n                          ...(selectedTags.includes(tag.id) && {\n                            background: `linear-gradient(135deg, ${tag.color}, ${tag.color}dd)`\n                          })\n                        }}\n                      >\n                        <Hash\n                          className=\"h-4 w-4\"\n                          style={{\n                            color: selectedTags.includes(tag.id) ? 'white' : tag.color\n                          }}\n                        />\n                      </div>\n                      <span className=\"truncate font-medium\">{tag.name}</span>\n                    </div>\n                    <Badge\n                      variant=\"secondary\"\n                      className={cn(\n                        \"ml-auto font-bold transition-all duration-300\",\n                        selectedTags.includes(tag.id) && \"text-white shadow-md\"\n                      )}\n                      style={{\n                        ...(selectedTags.includes(tag.id) && {\n                          background: tag.color,\n                          color: 'white'\n                        })\n                      }}\n                    >\n                      {getTagCount(tag.id)}\n                    </Badge>\n                  </SidebarMenuButton>\n\n                  <DropdownMenu>\n                    <DropdownMenuTrigger asChild>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"h-8 w-8 p-0 opacity-60 hover:opacity-100 transition-opacity\"\n                        disabled={isDeleting === tag.id}\n                      >\n                        <MoreHorizontal className=\"h-4 w-4\" />\n                      </Button>\n                    </DropdownMenuTrigger>\n                    <DropdownMenuContent align=\"end\" className=\"glass-effect border-border/50\">\n                      <DropdownMenuItem onClick={() => handleEditTag(tag)} className=\"hover:bg-blue-500/10\">\n                        <Edit className=\"h-4 w-4 mr-2\" />\n                        编辑标签\n                      </DropdownMenuItem>\n                      <DropdownMenuSeparator />\n                      <DropdownMenuItem\n                        onClick={() => handleDeleteTag(tag.id)}\n                        className=\"text-destructive focus:text-destructive hover:bg-red-500/10\"\n                        disabled={isDeleting === tag.id}\n                      >\n                        <Trash2 className=\"h-4 w-4 mr-2\" />\n                        删除标签\n                      </DropdownMenuItem>\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                </div>\n              </SidebarMenuItem>\n            ))}\n          </SidebarMenu>\n\n          {tags.length === 0 && (\n            <div className=\"text-sm text-muted-foreground/60 text-center py-6 px-4 rounded-xl bg-gradient-to-br from-gray-50/50 to-gray-100/30 dark:from-gray-800/30 dark:to-gray-900/20 border border-dashed border-border/50\">\n              <Hash className=\"h-8 w-8 mx-auto mb-2 text-muted-foreground/40\" />\n              <p>暂无标签</p>\n              <p className=\"text-xs mt-1\">点击上方 + 号创建标签</p>\n            </div>\n          )}\n        </div>\n\n        {/* 统计信息 */}\n        <div className=\"pt-4 border-t border-border/30\">\n          <div className=\"p-4 rounded-xl bg-gradient-to-br from-gray-50/80 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-900/30 border border-border/30\">\n            <h4 className=\"text-xs font-bold text-muted-foreground/80 uppercase tracking-wider mb-3\">统计信息</h4>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-600\"></div>\n                  <span className=\"text-xs font-medium\">总提示词</span>\n                </div>\n                <span className=\"text-xs font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent\">\n                  {prompts.length}\n                </span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-2 h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-600\"></div>\n                  <span className=\"text-xs font-medium\">分类数</span>\n                </div>\n                <span className=\"text-xs font-bold bg-gradient-to-r from-green-500 to-emerald-600 bg-clip-text text-transparent\">\n                  {categories.length}\n                </span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-600\"></div>\n                  <span className=\"text-xs font-medium\">标签数</span>\n                </div>\n                <span className=\"text-xs font-bold bg-gradient-to-r from-purple-500 to-pink-600 bg-clip-text text-transparent\">\n                  {tags.length}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </SidebarContent>\n\n      <SidebarRail />\n    </Sidebar>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAYA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;;;AAtCA;;;;;;;;;;;;AAwCO,SAAS;;IACd,MAAM,EACJ,UAAU,EACV,IAAI,EACJ,OAAO,EACP,gBAAgB,EAChB,YAAY,EACZ,mBAAmB,EACnB,eAAe,EACf,aAAa,EACb,OAAO,EACR,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD;IAC9C,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IACpC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,eAAe;IACf,MAAM,mBAAmB,CAAC;QACxB,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,UAAU,KAAK,YAAY,MAAM;IAC1E;IAEA,eAAe;IACf,MAAM,cAAc,CAAC;QACnB,OAAO,QAAQ,MAAM,CAAC,CAAA;gBACpB;oBAAA,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;WACpC,MAAM;IACV;IAEA,SAAS;IACT,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,MAAM,kBAAkB,aAAa,QAAQ,CAAC,SAC1C,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO,SACjC;eAAI;YAAc;SAAM;QAC5B,gBAAgB;IAClB;IAEA,OAAO;IACP,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;IACrB;IAEA,OAAO;IACP,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,QAAQ,kCAAkC;YAC7C;QACF;QAEA,IAAI;YACF,cAAc;YACd,MAAM,oHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;YAE3B,SAAS;YACT,MAAM,oBAAoB,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YAC9D,cAAc;YAEd,oBAAoB;YACpB,IAAI,qBAAqB,YAAY;gBACnC,oBAAoB;YACtB;YAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,OAAO;IACP,MAAM,gBAAgB,CAAC;QACrB,cAAc;IAChB;IAEA,OAAO;IACP,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,QAAQ,kCAAkC;YAC7C;QACF;QAEA,IAAI;YACF,cAAc;YACd,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YAErB,SAAS;YACT,MAAM,cAAc,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YAClD,QAAQ;YAER,oBAAoB;YACpB,IAAI,aAAa,QAAQ,CAAC,QAAQ;gBAChC,gBAAgB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO;YACnD;YAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,WAAU;;0BACjB,6LAAC,sIAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAEtB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAiI;;;;;;8CAG/I,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;0BAKtD,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,WAAU;;kCAExB,6LAAC,sIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,sIAAA,CAAA,kBAAe;sCACd,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;gCAChB,SAAS,IAAM,qBAAqB;gCACpC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA,wEACA,sCACA,qBAAqB,QAAQ;;kDAG/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gDACA,qBAAqB,OACjB,uEACA;0DAEJ,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,6LAAC,oIAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,qBAAqB,OACjB,sEACA;kDAGL,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;kCAOvB,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsE;;;;;;kDACpF,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM;wCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,0EACA;kDAGF,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIpB,6LAAC,sIAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,sIAAA,CAAA,kBAAe;kDACd,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,sIAAA,CAAA,oBAAiB;oDAChB,SAAS,IAAM,qBAAqB,SAAS,EAAE;oDAC/C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA,6DACA,qBAAqB,SAAS,EAAE,GAC5B,qBACA;oDAEN,OAAO;wDACL,GAAI,qBAAqB,SAAS,EAAE,IAAI;4DACtC,YAAY,AAAC,2BAA+C,OAArB,SAAS,KAAK,EAAC,QAAqB,OAAf,SAAS,KAAK,EAAC;4DAC3E,aAAa,AAAC,GAAiB,OAAf,SAAS,KAAK,EAAC;4DAC/B,WAAW,AAAC,cAA4B,OAAf,SAAS,KAAK,EAAC;wDAC1C,CAAC;oDACH;;sEAEA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,qBAAqB,SAAS,EAAE,GAC5B,yBACA;oEAEN,OAAO;wEACL,GAAI,qBAAqB,SAAS,EAAE,IAAI;4EACtC,YAAY,AAAC,2BAA6C,OAAnB,SAAS,KAAK,EAAC,MAAmB,OAAf,SAAS,KAAK,EAAC;wEAC3E,CAAC;oEACH;8EAEC,CAAC;wEACA,MAAM,gBAAgB,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,IAAI,EAAE,qBAAqB,SAAS,EAAE;wEACtF,qBAAO,6LAAC;4EAAc,WAAU;4EAAU,OAAO;gFAAE,OAAO,qBAAqB,SAAS,EAAE,GAAG,UAAU,SAAS,KAAK;4EAAC;;;;;;oEACxH,CAAC;;;;;;8EAEH,6LAAC;oEAAK,WAAU;8EAAwB,SAAS,IAAI;;;;;;;;;;;;sEAEvD,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,qBAAqB,SAAS,EAAE,IAAI;4DAEtC,OAAO;gEACL,GAAI,qBAAqB,SAAS,EAAE,IAAI;oEACtC,YAAY,SAAS,KAAK;oEAC1B,OAAO;gEACT,CAAC;4DACH;sEAEC,iBAAiB,SAAS,EAAE;;;;;;;;;;;;8DAKjC,6LAAC,+IAAA,CAAA,eAAY;;sEACX,6LAAC,+IAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gCACA,sEACA;gEAEF,UAAU,eAAe,SAAS,EAAE;0EAEpC,cAAA,6LAAC,mNAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG9B,6LAAC,+IAAA,CAAA,sBAAmB;4DAAC,OAAM;4DAAM,WAAU;;8EACzC,6LAAC,+IAAA,CAAA,mBAAgB;oEAAC,SAAS,IAAM,mBAAmB;oEAAW,WAAU;;sFACvE,6LAAC,8MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGnC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8EACtB,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,qBAAqB,SAAS,EAAE;oEAC/C,WAAU;oEACV,UAAU,eAAe,SAAS,EAAE;;sFAEpC,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;uCArFvB,SAAS,EAAE;;;;;;;;;;4BA+FpC,WAAW,MAAM,KAAK,mBACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAE;;;;;;kDACH,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;kCAMlC,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsE;;;;;;kDACpF,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM;wCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,wEACA;kDAGF,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIpB,6LAAC,sIAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC,sIAAA,CAAA,kBAAe;kDACd,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,sIAAA,CAAA,oBAAiB;oDAChB,SAAS,IAAM,gBAAgB,IAAI,EAAE;oDACrC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA,6DACA,aAAa,QAAQ,CAAC,IAAI,EAAE,IACxB,qBACA;oDAEN,OAAO;wDACL,GAAI,aAAa,QAAQ,CAAC,IAAI,EAAE,KAAK;4DACnC,YAAY,AAAC,2BAA0C,OAAhB,IAAI,KAAK,EAAC,QAAgB,OAAV,IAAI,KAAK,EAAC;4DACjE,aAAa,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;4DAC1B,WAAW,AAAC,cAAuB,OAAV,IAAI,KAAK,EAAC;wDACrC,CAAC;oDACH;;sEAEA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,aAAa,QAAQ,CAAC,IAAI,EAAE,IACxB,yBACA;oEAEN,OAAO;wEACL,GAAI,aAAa,QAAQ,CAAC,IAAI,EAAE,KAAK;4EACnC,YAAY,AAAC,2BAAwC,OAAd,IAAI,KAAK,EAAC,MAAc,OAAV,IAAI,KAAK,EAAC;wEACjE,CAAC;oEACH;8EAEA,cAAA,6LAAC,qMAAA,CAAA,OAAI;wEACH,WAAU;wEACV,OAAO;4EACL,OAAO,aAAa,QAAQ,CAAC,IAAI,EAAE,IAAI,UAAU,IAAI,KAAK;wEAC5D;;;;;;;;;;;8EAGJ,6LAAC;oEAAK,WAAU;8EAAwB,IAAI,IAAI;;;;;;;;;;;;sEAElD,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,aAAa,QAAQ,CAAC,IAAI,EAAE,KAAK;4DAEnC,OAAO;gEACL,GAAI,aAAa,QAAQ,CAAC,IAAI,EAAE,KAAK;oEACnC,YAAY,IAAI,KAAK;oEACrB,OAAO;gEACT,CAAC;4DACH;sEAEC,YAAY,IAAI,EAAE;;;;;;;;;;;;8DAIvB,6LAAC,+IAAA,CAAA,eAAY;;sEACX,6LAAC,+IAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,UAAU,eAAe,IAAI,EAAE;0EAE/B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG9B,6LAAC,+IAAA,CAAA,sBAAmB;4DAAC,OAAM;4DAAM,WAAU;;8EACzC,6LAAC,+IAAA,CAAA,mBAAgB;oEAAC,SAAS,IAAM,cAAc;oEAAM,WAAU;;sFAC7D,6LAAC,8MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGnC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8EACtB,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,gBAAgB,IAAI,EAAE;oEACrC,WAAU;oEACV,UAAU,eAAe,IAAI,EAAE;;sFAE/B,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;uCAjFvB,IAAI,EAAE;;;;;;;;;;4BA2F/B,KAAK,MAAM,KAAK,mBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAE;;;;;;kDACH,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;kCAMlC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2E;;;;;;8CACzF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;;;;;;;8DAExC,6LAAC;oDAAK,WAAU;8DACb,QAAQ,MAAM;;;;;;;;;;;;sDAGnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;;;;;;;8DAExC,6LAAC;oDAAK,WAAU;8DACb,WAAW,MAAM;;;;;;;;;;;;sDAGtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;;;;;;;8DAExC,6LAAC;oDAAK,WAAU;8DACb,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxB,6LAAC,sIAAA,CAAA,cAAW;;;;;;;;;;;AAGlB;GA/bgB;;QAWV,sHAAA,CAAA,iBAAc;QAEa,sHAAA,CAAA,mBAAgB;QACrB,sHAAA,CAAA,cAAW;;;KAdvB", "debugId": null}}, {"offset": {"line": 3117, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 3239, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/prompt-card.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { usePromptStore } from \"@/lib/store\";\nimport { promptsApi } from \"@/lib/api\";\nimport type { Prompt } from \"@/lib/api\";\nimport { <PERSON>, CardContent, CardFooter, CardHeader } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  Copy,\n  Edit,\n  Eye,\n  MoreVertical,\n  Trash2,\n  Calendar,\n  TrendingUp,\n  Pin,\n  PinOff,\n  CheckSquare,\n  Square\n} from \"lucide-react\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { toast } from \"sonner\";\nimport { cn } from \"@/lib/utils\";\n\ninterface PromptCardProps {\n  prompt: Prompt;\n}\n\nexport function PromptCard({ prompt }: PromptCardProps) {\n  const {\n    openPromptDialog,\n    setPrompts,\n    prompts,\n    // 🚀 批量操作相关状态\n    isBatchMode,\n    selectedPromptIds,\n    togglePromptSelection\n  } = usePromptStore();\n  const [isLoading, setIsLoading] = useState(false);\n  const [isCopying, setIsCopying] = useState(false);\n\n  // 是否被选中\n  const isSelected = selectedPromptIds.includes(prompt.id);\n\n  // 复制提示词内容\n  const handleCopy = async (e: React.MouseEvent) => {\n    e.stopPropagation();\n\n    if (isCopying) return; // 防止重复点击\n\n    setIsCopying(true);\n\n    try {\n      // 立即复制到剪贴板并显示反馈\n      await navigator.clipboard.writeText(prompt.content);\n      toast.success(\"已复制到剪贴板\");\n\n      // 在后台异步增加使用次数，不阻塞用户体验\n      promptsApi.incrementUsage(prompt.id).then(() => {\n        // 更新本地状态\n        const updatedPrompts = prompts.map(p =>\n          p.id === prompt.id\n            ? { ...p, usageCount: p.usageCount + 1 }\n            : p\n        );\n        setPrompts(updatedPrompts);\n      }).catch((error) => {\n        console.error(\"更新使用次数失败:\", error);\n        // 不显示错误给用户，因为复制已经成功了\n      });\n\n    } catch (error) {\n      console.error(\"复制失败:\", error);\n      toast.error(\"复制失败\");\n    } finally {\n      // 短暂延迟后重置状态，防止按钮闪烁\n      setTimeout(() => setIsCopying(false), 500);\n    }\n  };\n\n  // 查看详情\n  const handleView = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    openPromptDialog(prompt, false);\n  };\n\n  // 卡片点击处理\n  const handleCardClick = () => {\n    if (isBatchMode) {\n      togglePromptSelection(prompt.id);\n    } else {\n      openPromptDialog(prompt);\n    }\n  };\n\n  // 编辑提示词\n  const handleEdit = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    openPromptDialog(prompt, true);\n  };\n\n  // 🚀 置顶/取消置顶\n  const handleTogglePin = async (e: React.MouseEvent) => {\n    e.stopPropagation();\n\n    try {\n      const updatedPrompt = await promptsApi.togglePin(prompt.id);\n\n      // 更新本地状态\n      const updatedPrompts = prompts.map(p =>\n        p.id === prompt.id ? updatedPrompt : p\n      );\n      setPrompts(updatedPrompts);\n\n      toast.success(updatedPrompt.isPinned ? \"已置顶\" : \"已取消置顶\");\n    } catch (error) {\n      console.error(\"切换置顶状态失败:\", error);\n      toast.error(\"操作失败\");\n    }\n  };\n\n  // 🚀 切换选择状态\n  const handleToggleSelection = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    togglePromptSelection(prompt.id);\n  };\n\n  // 删除提示词\n  const handleDelete = async (e: React.MouseEvent) => {\n    e.stopPropagation();\n    \n    if (!confirm(\"确定要删除这个提示词吗？\")) {\n      return;\n    }\n\n    try {\n      setIsLoading(true);\n      await promptsApi.delete(prompt.id);\n      \n      // 更新本地状态\n      const updatedPrompts = prompts.filter(p => p.id !== prompt.id);\n      setPrompts(updatedPrompts);\n      \n      toast.success(\"提示词已删除\");\n    } catch (error) {\n      console.error(\"删除失败:\", error);\n      toast.error(\"删除失败\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 格式化日期\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  // 智能截断文本 - 优先在句号、感叹号、问号处截断\n  const smartTruncateText = (text: string, maxLength: number) => {\n    if (text.length <= maxLength) return text;\n\n    // 在maxLength范围内查找最后的句号、感叹号或问号\n    const truncated = text.substring(0, maxLength);\n    const lastSentenceEnd = Math.max(\n      truncated.lastIndexOf('。'),\n      truncated.lastIndexOf('！'),\n      truncated.lastIndexOf('？'),\n      truncated.lastIndexOf('.'),\n      truncated.lastIndexOf('!'),\n      truncated.lastIndexOf('?')\n    );\n\n    // 如果找到句子结尾且位置合理（不少于maxLength的60%），在那里截断\n    if (lastSentenceEnd > maxLength * 0.6) {\n      return text.substring(0, lastSentenceEnd + 1);\n    }\n\n    // 否则在最后一个空格处截断，避免截断单词\n    const lastSpace = truncated.lastIndexOf(' ');\n    if (lastSpace > maxLength * 0.8) {\n      return text.substring(0, lastSpace) + '...';\n    }\n\n    // 最后选择：直接截断并添加省略号\n    return text.substring(0, maxLength) + '...';\n  };\n\n  return (\n    <Card\n      className={cn(\n        \"professional-card group cursor-pointer relative h-full flex flex-col\",\n        \"hover:shadow-lg hover:shadow-primary/10 hover:-translate-y-1 hover:scale-[1.02]\",\n        \"transition-all duration-300 ease-out\",\n        \"bg-card border-2\",\n        \"hover:border-primary/30\",\n        isLoading && \"opacity-50 pointer-events-none\",\n        // 🚀 批量模式样式\n        isBatchMode && \"hover:border-primary/50\",\n        isSelected && \"border-primary bg-primary/5 shadow-lg shadow-primary/20 scale-[1.02] -translate-y-1\"\n      )}\n      onClick={handleCardClick}\n    >\n      {/* 🚀 批量选择框 */}\n      {isBatchMode && (\n        <div className=\"absolute top-3 right-3 z-20\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={handleToggleSelection}\n            className=\"h-6 w-6 p-0 rounded-full bg-white/90 hover:bg-white border border-border/50 shadow-sm\"\n          >\n            {isSelected ? (\n              <CheckSquare className=\"h-4 w-4 text-primary\" />\n            ) : (\n              <Square className=\"h-4 w-4 text-muted-foreground\" />\n            )}\n          </Button>\n        </div>\n      )}\n\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-start gap-2 flex-1\">\n            <h3 className=\"font-bold text-base md:text-lg leading-tight line-clamp-2 text-foreground\">\n              {prompt.title}\n            </h3>\n            {prompt.isPinned && (\n              <Pin className=\"h-4 w-4 text-amber-500 shrink-0 mt-0.5\" title=\"已置顶\" />\n            )}\n          </div>\n\n          {!isBatchMode && (\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className={cn(\n                    \"h-8 w-8 p-0 shrink-0 rounded-full\",\n                    \"opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                    \"hover:bg-accent\"\n                  )}\n                  onClick={(e) => e.stopPropagation()}\n                >\n                  <MoreVertical className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\" className=\"border\">\n              <DropdownMenuItem onClick={handleView}>\n                <Eye className=\"h-4 w-4 mr-2\" />\n                查看详情\n              </DropdownMenuItem>\n              <DropdownMenuItem onClick={handleEdit}>\n                <Edit className=\"h-4 w-4 mr-2\" />\n                编辑\n              </DropdownMenuItem>\n              <DropdownMenuItem onClick={handleCopy} disabled={isCopying}>\n                {isCopying ? (\n                  <div className=\"h-4 w-4 mr-2 animate-spin rounded-full border-2 border-primary border-t-transparent\" />\n                ) : (\n                  <Copy className=\"h-4 w-4 mr-2\" />\n                )}\n                {isCopying ? \"复制中...\" : \"复制内容\"}\n              </DropdownMenuItem>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem onClick={handleTogglePin}>\n                {prompt.isPinned ? (\n                  <PinOff className=\"h-4 w-4 mr-2\" />\n                ) : (\n                  <Pin className=\"h-4 w-4 mr-2\" />\n                )}\n                {prompt.isPinned ? \"取消置顶\" : \"置顶\"}\n              </DropdownMenuItem>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem\n                onClick={handleDelete}\n                className=\"text-destructive focus:text-destructive\"\n              >\n                <Trash2 className=\"h-4 w-4 mr-2\" />\n                删除\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n            </DropdownMenu>\n          )}\n        </div>\n\n        {/* 描述区域 - 固定高度确保对齐 */}\n        <div className=\"mt-2 h-12 flex items-start\">\n          {prompt.description ? (\n            <p className=\"text-xs md:text-sm text-muted-foreground line-clamp-2 leading-relaxed\">\n              {prompt.description}\n            </p>\n          ) : (\n            <div className=\"h-12\" />\n          )}\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"pb-3 flex-1 flex flex-col\">\n        {/* 内容预览 - 固定高度确保对齐 */}\n        <div className=\"mb-4 h-20\">\n          <div className=\"h-full text-xs md:text-sm text-muted-foreground leading-relaxed p-3 rounded-md bg-muted border relative overflow-hidden w-full\">\n            <div className=\"line-clamp-3 h-full flex items-start\">\n              <span className=\"w-full\">\n                {smartTruncateText(prompt.content, 150)}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* 分类和标签 */}\n        <div className=\"flex flex-wrap gap-1.5 md:gap-2 mt-auto\">\n          {prompt.category && (\n            <Badge\n              variant=\"secondary\"\n              className=\"text-xs font-medium px-2.5 py-1 rounded-md border\"\n              style={{\n                backgroundColor: `${prompt.category.color}10`,\n                color: prompt.category.color,\n                borderColor: `${prompt.category.color}30`\n              }}\n            >\n              {prompt.category.name}\n            </Badge>\n          )}\n\n          {prompt.tags?.slice(0, 3).map((tag) => (\n            <Badge\n              key={tag.id}\n              variant=\"outline\"\n              className=\"text-xs font-medium px-2.5 py-1 rounded-md border\"\n              style={{\n                borderColor: `${tag.color}40`,\n                color: tag.color,\n                backgroundColor: `${tag.color}08`\n              }}\n            >\n              #{tag.name}\n            </Badge>\n          ))}\n\n          {prompt.tags && prompt.tags.length > 3 && (\n            <Badge\n              variant=\"outline\"\n              className=\"text-xs font-medium px-2.5 py-1 rounded-md border-dashed border-muted-foreground/30 text-muted-foreground\"\n            >\n              +{prompt.tags.length - 3}\n            </Badge>\n          )}\n        </div>\n      </CardContent>\n\n      <CardFooter className=\"pt-0 flex items-center justify-between relative z-10\">\n        {/* 统计信息 */}\n        <div className=\"flex items-center gap-4 text-xs text-muted-foreground/80\">\n          <div className=\"flex items-center gap-1.5 px-2 py-1 rounded-full bg-primary/10 border border-primary/20\">\n            <TrendingUp className=\"h-3 w-3 text-primary\" />\n            <span className=\"font-medium\">{prompt.usageCount}</span>\n          </div>\n          <div className=\"flex items-center gap-1.5 px-2 py-1 rounded-full bg-muted border border-border\">\n            <Calendar className=\"h-3 w-3 text-muted-foreground\" />\n            <span className=\"font-medium\">{formatDate(prompt.updatedAt)}</span>\n          </div>\n        </div>\n\n        {/* 快速操作按钮 */}\n        <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-0 translate-x-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={handleCopy}\n            disabled={isCopying}\n            className={cn(\n              \"h-8 w-8 p-0 rounded-full transition-all duration-200\",\n              \"hover:bg-accent\",\n              isCopying && \"bg-accent\"\n            )}\n            title={isCopying ? \"复制中...\" : \"复制内容\"}\n          >\n            {isCopying ? (\n              <div className=\"h-3 w-3 animate-spin rounded-full border-2 border-primary border-t-transparent\" />\n            ) : (\n              <Copy className=\"h-3 w-3\" />\n            )}\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={handleEdit}\n            className={cn(\n              \"h-8 w-8 p-0 rounded-full transition-all duration-200\",\n              \"hover:bg-accent\"\n            )}\n            title=\"编辑\"\n          >\n            <Edit className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      </CardFooter>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAOA;AACA;;;AA9BA;;;;;;;;;;;AAoCO,SAAS,WAAW,KAA2B;QAA3B,EAAE,MAAM,EAAmB,GAA3B;QA8ShB;;IA7ST,MAAM,EACJ,gBAAgB,EAChB,UAAU,EACV,OAAO,EACP,cAAc;IACd,WAAW,EACX,iBAAiB,EACjB,qBAAqB,EACtB,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,QAAQ;IACR,MAAM,aAAa,kBAAkB,QAAQ,CAAC,OAAO,EAAE;IAEvD,UAAU;IACV,MAAM,aAAa,OAAO;QACxB,EAAE,eAAe;QAEjB,IAAI,WAAW,QAAQ,SAAS;QAEhC,aAAa;QAEb,IAAI;YACF,gBAAgB;YAChB,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,OAAO;YAClD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,sBAAsB;YACtB,oHAAA,CAAA,aAAU,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;gBACxC,SAAS;gBACT,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,IACjC,EAAE,EAAE,KAAK,OAAO,EAAE,GACd;wBAAE,GAAG,CAAC;wBAAE,YAAY,EAAE,UAAU,GAAG;oBAAE,IACrC;gBAEN,WAAW;YACb,GAAG,KAAK,CAAC,CAAC;gBACR,QAAQ,KAAK,CAAC,aAAa;YAC3B,qBAAqB;YACvB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,mBAAmB;YACnB,WAAW,IAAM,aAAa,QAAQ;QACxC;IACF;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,EAAE,eAAe;QACjB,iBAAiB,QAAQ;IAC3B;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,IAAI,aAAa;YACf,sBAAsB,OAAO,EAAE;QACjC,OAAO;YACL,iBAAiB;QACnB;IACF;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,EAAE,eAAe;QACjB,iBAAiB,QAAQ;IAC3B;IAEA,aAAa;IACb,MAAM,kBAAkB,OAAO;QAC7B,EAAE,eAAe;QAEjB,IAAI;YACF,MAAM,gBAAgB,MAAM,oHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,OAAO,EAAE;YAE1D,SAAS;YACT,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,IACjC,EAAE,EAAE,KAAK,OAAO,EAAE,GAAG,gBAAgB;YAEvC,WAAW;YAEX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,cAAc,QAAQ,GAAG,QAAQ;QACjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,YAAY;IACZ,MAAM,wBAAwB,CAAC;QAC7B,EAAE,eAAe;QACjB,sBAAsB,OAAO,EAAE;IACjC;IAEA,QAAQ;IACR,MAAM,eAAe,OAAO;QAC1B,EAAE,eAAe;QAEjB,IAAI,CAAC,QAAQ,iBAAiB;YAC5B;QACF;QAEA,IAAI;YACF,aAAa;YACb,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,OAAO,EAAE;YAEjC,SAAS;YACT,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;YAC7D,WAAW;YAEX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,CAAC,MAAc;QACvC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QAErC,8BAA8B;QAC9B,MAAM,YAAY,KAAK,SAAS,CAAC,GAAG;QACpC,MAAM,kBAAkB,KAAK,GAAG,CAC9B,UAAU,WAAW,CAAC,MACtB,UAAU,WAAW,CAAC,MACtB,UAAU,WAAW,CAAC,MACtB,UAAU,WAAW,CAAC,MACtB,UAAU,WAAW,CAAC,MACtB,UAAU,WAAW,CAAC;QAGxB,wCAAwC;QACxC,IAAI,kBAAkB,YAAY,KAAK;YACrC,OAAO,KAAK,SAAS,CAAC,GAAG,kBAAkB;QAC7C;QAEA,sBAAsB;QACtB,MAAM,YAAY,UAAU,WAAW,CAAC;QACxC,IAAI,YAAY,YAAY,KAAK;YAC/B,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;QACxC;QAEA,kBAAkB;QAClB,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;IACxC;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,mFACA,wCACA,oBACA,2BACA,aAAa,kCACb,YAAY;QACZ,eAAe,2BACf,cAAc;QAEhB,SAAS;;YAGR,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;8BAET,2BACC,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;6CAEvB,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAM1B,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,OAAO,KAAK;;;;;;oCAEd,OAAO,QAAQ,kBACd,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;wCAAyC,OAAM;;;;;;;;;;;;4BAIjE,CAAC,6BACA,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,qEACA;4CAEF,SAAS,CAAC,IAAM,EAAE,eAAe;sDAEjC,cAAA,6LAAC,6NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG9B,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;;kEACzB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;;kEACzB,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;gDAAY,UAAU;;oDAC9C,0BACC,6LAAC;wDAAI,WAAU;;;;;6EAEf,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAEjB,YAAY,WAAW;;;;;;;0DAE1B,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;;oDACxB,OAAO,QAAQ,iBACd,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAEhB,OAAO,QAAQ,GAAG,SAAS;;;;;;;0DAE9B,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3C,6LAAC;wBAAI,WAAU;kCACZ,OAAO,WAAW,iBACjB,6LAAC;4BAAE,WAAU;sCACV,OAAO,WAAW;;;;;iDAGrB,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAKrB,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CACb,kBAAkB,OAAO,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;kCAO3C,6LAAC;wBAAI,WAAU;;4BACZ,OAAO,QAAQ,kBACd,6LAAC,oIAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAU;gCACV,OAAO;oCACL,iBAAiB,AAAC,GAAwB,OAAtB,OAAO,QAAQ,CAAC,KAAK,EAAC;oCAC1C,OAAO,OAAO,QAAQ,CAAC,KAAK;oCAC5B,aAAa,AAAC,GAAwB,OAAtB,OAAO,QAAQ,CAAC,KAAK,EAAC;gCACxC;0CAEC,OAAO,QAAQ,CAAC,IAAI;;;;;;6BAIxB,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC7B,6LAAC,oIAAA,CAAA,QAAK;oCAEJ,SAAQ;oCACR,WAAU;oCACV,OAAO;wCACL,aAAa,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;wCAC1B,OAAO,IAAI,KAAK;wCAChB,iBAAiB,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;oCAChC;;wCACD;wCACG,IAAI,IAAI;;mCATL,IAAI,EAAE;;;;;4BAad,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,mBACnC,6LAAC,oIAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAU;;oCACX;oCACG,OAAO,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;0BAM/B,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCAEpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAK,WAAU;kDAAe,OAAO,UAAU;;;;;;;;;;;;0CAElD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAe,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;kCAK9D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA,mBACA,aAAa;gCAEf,OAAO,YAAY,WAAW;0CAE7B,0BACC,6LAAC;oCAAI,WAAU;;;;;yDAEf,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGpB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;gCAEF,OAAM;0CAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GAzXgB;;QASV,sHAAA,CAAA,iBAAc;;;KATJ", "debugId": null}}, {"offset": {"line": 3880, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/empty-state.tsx"], "sourcesContent": ["\"use client\";\n\nimport { usePromptStore } from \"@/lib/store\";\nimport { Button } from \"@/components/ui/button\";\nimport { \n  FileText, \n  Search, \n  Plus,\n  Filter\n} from \"lucide-react\";\n\ninterface EmptyStateProps {\n  hasFilters: boolean;\n}\n\nexport function EmptyState({ hasFilters }: EmptyStateProps) {\n  const { \n    openPromptDialog, \n    setSearchQuery, \n    setSelectedCategory, \n    setSelectedTags \n  } = usePromptStore();\n\n  const clearFilters = () => {\n    setSearchQuery(\"\");\n    setSelectedCategory(null);\n    setSelectedTags([]);\n  };\n\n  if (hasFilters) {\n    return (\n      <div className=\"flex flex-col items-center justify-center h-64 text-center\">\n        <div className=\"w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4\">\n          <Search className=\"h-8 w-8 text-muted-foreground\" />\n        </div>\n        <h3 className=\"text-lg font-semibold mb-2\">未找到匹配的提示词</h3>\n        <p className=\"text-muted-foreground mb-4 max-w-md\">\n          没有找到符合当前筛选条件的提示词。试试调整搜索关键词或筛选条件。\n        </p>\n        <div className=\"flex gap-2\">\n          <Button variant=\"outline\" onClick={clearFilters}>\n            <Filter className=\"h-4 w-4 mr-2\" />\n            清除筛选\n          </Button>\n          <Button onClick={() => openPromptDialog()}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            新建提示词\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex flex-col items-center justify-center h-64 text-center\">\n      <div className=\"w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4\">\n        <FileText className=\"h-8 w-8 text-muted-foreground\" />\n      </div>\n      <h3 className=\"text-lg font-semibold mb-2\">还没有提示词</h3>\n      <p className=\"text-muted-foreground mb-4 max-w-md\">\n        开始创建你的第一个提示词，构建你的个人提示词库。\n      </p>\n      <Button onClick={() => openPromptDialog()}>\n        <Plus className=\"h-4 w-4 mr-2\" />\n        创建第一个提示词\n      </Button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAeO,SAAS,WAAW,KAA+B;QAA/B,EAAE,UAAU,EAAmB,GAA/B;;IACzB,MAAM,EACJ,gBAAgB,EAChB,cAAc,EACd,mBAAmB,EACnB,eAAe,EAChB,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,eAAe;QACnB,eAAe;QACf,oBAAoB;QACpB,gBAAgB,EAAE;IACpB;IAEA,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;8BAEpB,6LAAC;oBAAG,WAAU;8BAA6B;;;;;;8BAC3C,6LAAC;oBAAE,WAAU;8BAAsC;;;;;;8BAGnD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;;8CACjC,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGrC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM;;8CACrB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;IAM3C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAEtB,6LAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,6LAAC;gBAAE,WAAU;0BAAsC;;;;;;0BAGnD,6LAAC,qIAAA,CAAA,SAAM;gBAAC,SAAS,IAAM;;kCACrB,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAKzC;GArDgB;;QAMV,sHAAA,CAAA,iBAAc;;;KANJ", "debugId": null}}, {"offset": {"line": 4064, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/prompt-grid.tsx"], "sourcesContent": ["\"use client\";\n\nimport { usePromptStore } from \"@/lib/store\";\nimport { PromptCard } from \"@/components/prompt-card\";\nimport { EmptyState } from \"@/components/empty-state\";\n\nexport function PromptGrid() {\n  const { filteredPrompts, searchQuery, selectedCategory, selectedTags } = usePromptStore();\n\n  const prompts = filteredPrompts();\n\n  if (prompts.length === 0) {\n    return (\n      <EmptyState \n        hasFilters={!!(searchQuery || selectedCategory || selectedTags.length > 0)}\n      />\n    );\n  }\n\n  return (\n    <div className=\"responsive-grid fixed-height animate-fade-in\">\n      {prompts.map((prompt, index) => (\n        <div\n          key={prompt.id}\n          className=\"card-container animate-slide-up\"\n          style={{\n            animationDelay: `${index * 25}ms`,\n            animationFillMode: 'both'\n          }}\n        >\n          <PromptCard prompt={prompt} />\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,gBAAgB,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IAEtF,MAAM,UAAU;IAEhB,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,qBACE,6LAAC,uIAAA,CAAA,aAAU;YACT,YAAY,CAAC,CAAC,CAAC,eAAe,oBAAoB,aAAa,MAAM,GAAG,CAAC;;;;;;IAG/E;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;gBAEC,WAAU;gBACV,OAAO;oBACL,gBAAgB,AAAC,GAAa,OAAX,QAAQ,IAAG;oBAC9B,mBAAmB;gBACrB;0BAEA,cAAA,6LAAC,uIAAA,CAAA,aAAU;oBAAC,QAAQ;;;;;;eAPf,OAAO,EAAE;;;;;;;;;;AAYxB;GA7BgB;;QAC2D,sHAAA,CAAA,iBAAc;;;KADzE", "debugId": null}}, {"offset": {"line": 4133, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,KAEoC;QAFpC,EACf,GAAG,OACgD,GAFpC;IAGf,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,KAEgC;QAFhC,EACtB,GAAG,OACmD,GAFhC;IAGtB,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,KAKgC;QALhC,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD,GALhC;IAMtB,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 4218, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/search-bar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { usePromptStore } from \"@/lib/store\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  Search,\n  X,\n  SlidersHorizontal\n} from \"lucide-react\";\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\";\nimport { cn } from \"@/lib/utils\";\n\nexport function SearchBar() {\n  const {\n    searchQuery,\n    setSearchQuery,\n    selectedCategory,\n    selectedTags,\n    categories,\n    tags,\n    setSelectedCategory,\n    setSelectedTags,\n    filteredPrompts,\n  } = usePromptStore();\n\n  const [localQuery, setLocalQuery] = useState(searchQuery);\n  const [isFilterOpen, setIsFilterOpen] = useState(false);\n\n  // 防抖搜索\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setSearchQuery(localQuery);\n    }, 300);\n\n    return () => clearTimeout(timer);\n  }, [localQuery, setSearchQuery]);\n\n  // 清除搜索\n  const clearSearch = () => {\n    setLocalQuery(\"\");\n    setSearchQuery(\"\");\n  };\n\n  // 清除所有筛选\n  const clearAllFilters = () => {\n    setSelectedCategory(null);\n    setSelectedTags([]);\n    clearSearch();\n  };\n\n  // 获取选中的分类名称\n  const getSelectedCategoryName = () => {\n    if (!selectedCategory) return null;\n    return categories.find(cat => cat.id === selectedCategory)?.name;\n  };\n\n\n\n  // 计算活跃筛选器数量\n  const activeFiltersCount = \n    (selectedCategory ? 1 : 0) + \n    selectedTags.length + \n    (searchQuery ? 1 : 0);\n\n  return (\n    <div className=\"flex items-center gap-2 md:gap-3 flex-1 max-w-2xl\">\n      {/* 搜索输入框 */}\n      <div className=\"relative flex-1\">\n        <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 p-1 rounded-md bg-gradient-to-r from-blue-500/20 to-purple-500/20\">\n          <Search className=\"h-3.5 w-3.5 text-blue-600 dark:text-blue-400\" />\n        </div>\n        <Input\n          placeholder=\"🔍 搜索标题、内容、描述、分类、标签...\"\n          value={localQuery}\n          onChange={(e) => setLocalQuery(e.target.value)}\n          className={cn(\n            \"modern-input pl-12 pr-10 text-sm md:text-base\",\n            \"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\",\n            \"border-border/50 focus:border-primary/50\",\n            \"shadow-sm hover:shadow-md transition-all duration-300\",\n            \"placeholder:text-muted-foreground/60\"\n          )}\n          title=\"支持全文搜索：标题、内容、描述、分类名称、标签名称\"\n        />\n        {localQuery && (\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={clearSearch}\n            className={cn(\n              \"absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 rounded-full\",\n              \"hover:bg-red-500/20 hover:text-red-600 transition-all duration-200\",\n              \"hover:scale-110\"\n            )}\n          >\n            <X className=\"h-3.5 w-3.5\" />\n          </Button>\n        )}\n      </div>\n\n      {/* 筛选器按钮 */}\n      <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>\n        <PopoverTrigger asChild>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            className={cn(\n              \"gap-1 md:gap-2 shrink-0 rounded-xl transition-all duration-300\",\n              \"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\",\n              \"border-border/50 hover:border-primary/50\",\n              \"shadow-sm hover:shadow-md hover:scale-105\",\n              activeFiltersCount > 0 && \"border-primary/60 bg-gradient-to-r from-blue-500/10 to-purple-500/10\"\n            )}\n          >\n            <SlidersHorizontal className=\"h-4 w-4\" />\n            <span className=\"hidden sm:inline font-medium\">筛选</span>\n            {activeFiltersCount > 0 && (\n              <Badge\n                variant=\"secondary\"\n                className=\"ml-1 h-5 min-w-5 text-xs font-bold bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0\"\n              >\n                {activeFiltersCount}\n              </Badge>\n            )}\n          </Button>\n        </PopoverTrigger>\n        <PopoverContent className=\"w-80 glass-effect border-border/50 shadow-xl\" align=\"end\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <h4 className=\"font-medium\">筛选条件</h4>\n              {activeFiltersCount > 0 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={clearAllFilters}\n                  className=\"h-auto p-1 text-xs\"\n                >\n                  清除全部\n                </Button>\n              )}\n            </div>\n\n            {/* 分类筛选 */}\n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">分类</label>\n              <div className=\"flex flex-wrap gap-2\">\n                {categories.map((category) => (\n                  <Badge\n                    key={category.id}\n                    variant={selectedCategory === category.id ? \"default\" : \"outline\"}\n                    className=\"cursor-pointer\"\n                    onClick={() => \n                      setSelectedCategory(\n                        selectedCategory === category.id ? null : category.id\n                      )\n                    }\n                  >\n                    {category.name}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n\n            {/* 标签筛选 */}\n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">标签</label>\n              <div className=\"flex flex-wrap gap-2\">\n                {tags.map((tag) => (\n                  <Badge\n                    key={tag.id}\n                    variant={selectedTags.includes(tag.id) ? \"default\" : \"outline\"}\n                    className=\"cursor-pointer\"\n                    onClick={() => {\n                      const newSelectedTags = selectedTags.includes(tag.id)\n                        ? selectedTags.filter(id => id !== tag.id)\n                        : [...selectedTags, tag.id];\n                      setSelectedTags(newSelectedTags);\n                    }}\n                  >\n                    #{tag.name}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n\n            {/* 结果统计 */}\n            <div className=\"pt-2 border-t\">\n              <div className=\"text-sm text-muted-foreground\">\n                找到 <span className=\"font-medium\">{filteredPrompts().length}</span> 个提示词\n              </div>\n            </div>\n          </div>\n        </PopoverContent>\n      </Popover>\n\n      {/* 活跃筛选器显示 */}\n      {activeFiltersCount > 0 && (\n        <div className=\"flex items-center gap-2\">\n          {/* 分类筛选器 */}\n          {selectedCategory && (\n            <Badge variant=\"secondary\" className=\"gap-1\">\n              分类: {getSelectedCategoryName()}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => setSelectedCategory(null)}\n              />\n            </Badge>\n          )}\n\n          {/* 标签筛选器 */}\n          {selectedTags.map((tagId) => {\n            const tag = tags.find(t => t.id === tagId);\n            if (!tag) return null;\n            return (\n              <Badge key={tagId} variant=\"secondary\" className=\"gap-1\">\n                #{tag.name}\n                <X\n                  className=\"h-3 w-3 cursor-pointer\"\n                  onClick={() => {\n                    setSelectedTags(selectedTags.filter(id => id !== tagId));\n                  }}\n                />\n              </Badge>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAKA;AAKA;;;AAjBA;;;;;;;;;AAmBO,SAAS;;IACd,MAAM,EACJ,WAAW,EACX,cAAc,EACd,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,IAAI,EACJ,mBAAmB,EACnB,eAAe,EACf,eAAe,EAChB,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,OAAO;IACP,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,QAAQ;6CAAW;oBACvB,eAAe;gBACjB;4CAAG;YAEH;uCAAO,IAAM,aAAa;;QAC5B;8BAAG;QAAC;QAAY;KAAe;IAE/B,OAAO;IACP,MAAM,cAAc;QAClB,cAAc;QACd,eAAe;IACjB;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,oBAAoB;QACpB,gBAAgB,EAAE;QAClB;IACF;IAEA,YAAY;IACZ,MAAM,0BAA0B;YAEvB;QADP,IAAI,CAAC,kBAAkB,OAAO;QAC9B,QAAO,mBAAA,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,+BAAlC,uCAAA,iBAAqD,IAAI;IAClE;IAIA,YAAY;IACZ,MAAM,qBACJ,CAAC,mBAAmB,IAAI,CAAC,IACzB,aAAa,MAAM,GACnB,CAAC,cAAc,IAAI,CAAC;IAEtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,6LAAC,oIAAA,CAAA,QAAK;wBACJ,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,oDACA,4CACA,yDACA;wBAEF,OAAM;;;;;;oBAEP,4BACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,sEACA;kCAGF,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAMnB,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAc,cAAc;;kCACzC,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,oDACA,4CACA,6CACA,qBAAqB,KAAK;;8CAG5B,6LAAC,mOAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;8CAC7B,6LAAC;oCAAK,WAAU;8CAA+B;;;;;;gCAC9C,qBAAqB,mBACpB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;8CAET;;;;;;;;;;;;;;;;;kCAKT,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAA+C,OAAM;kCAC7E,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAc;;;;;;wCAC3B,qBAAqB,mBACpB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAOL,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,oIAAA,CAAA,QAAK;oDAEJ,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oDACxD,WAAU;oDACV,SAAS,IACP,oBACE,qBAAqB,SAAS,EAAE,GAAG,OAAO,SAAS,EAAE;8DAIxD,SAAS,IAAI;mDATT,SAAS,EAAE;;;;;;;;;;;;;;;;8CAgBxB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;sDACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC,oIAAA,CAAA,QAAK;oDAEJ,SAAS,aAAa,QAAQ,CAAC,IAAI,EAAE,IAAI,YAAY;oDACrD,WAAU;oDACV,SAAS;wDACP,MAAM,kBAAkB,aAAa,QAAQ,CAAC,IAAI,EAAE,IAChD,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO,IAAI,EAAE,IACvC;+DAAI;4DAAc,IAAI,EAAE;yDAAC;wDAC7B,gBAAgB;oDAClB;;wDACD;wDACG,IAAI,IAAI;;mDAVL,IAAI,EAAE;;;;;;;;;;;;;;;;8CAiBnB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;4CAAgC;0DAC1C,6LAAC;gDAAK,WAAU;0DAAe,kBAAkB,MAAM;;;;;;4CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ3E,qBAAqB,mBACpB,6LAAC;gBAAI,WAAU;;oBAEZ,kCACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAQ;4BACtC;0CACL,6LAAC,+LAAA,CAAA,IAAC;gCACA,WAAU;gCACV,SAAS,IAAM,oBAAoB;;;;;;;;;;;;oBAMxC,aAAa,GAAG,CAAC,CAAC;wBACjB,MAAM,MAAM,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wBACpC,IAAI,CAAC,KAAK,OAAO;wBACjB,qBACE,6LAAC,oIAAA,CAAA,QAAK;4BAAa,SAAQ;4BAAY,WAAU;;gCAAQ;gCACrD,IAAI,IAAI;8CACV,6LAAC,+LAAA,CAAA,IAAC;oCACA,WAAU;oCACV,SAAS;wCACP,gBAAgB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO;oCACnD;;;;;;;2BANQ;;;;;oBAUhB;;;;;;;;;;;;;AAKV;GAzNgB;;QAWV,sHAAA,CAAA,iBAAc;;;KAXJ", "debugId": null}}, {"offset": {"line": 4614, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/90 backdrop-blur-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-card data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border-4 border-primary/30 p-6 shadow-2xl duration-200 ring-4 ring-primary/10\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2KACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yYACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 4822, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,KAGoC;QAHpC,EACZ,SAAS,EACT,GAAG,OAC6C,GAHpC;IAIZ,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,KAGgC;QAHhC,EAChB,SAAS,EACT,GAAG,OAC6C,GAHhC;IAIhB,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 4902, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-3 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-3 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-slate-400 hover:bg-slate-500 transition-colors\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,6JAAA,CAAA,aAAgB,MAGjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,6JAAA,CAAA,aAAgB,CAGhC,QAAoD;QAAnD,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO;yBAClD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,oDACF,gBAAgB,gBACd,sDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4985, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-slate-400 placeholder:text-muted-foreground focus-visible:border-primary focus-visible:ring-primary/30 aria-invalid:ring-destructive/20 aria-invalid:border-destructive bg-white flex field-sizing-content min-h-16 w-full rounded-md border-2 px-3 py-2 text-base shadow-md transition-[color,box-shadow] outline-none focus-visible:ring-4 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kZACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 5017, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 5052, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-slate-400 data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-primary focus-visible:ring-primary/30 aria-invalid:ring-destructive/20 aria-invalid:border-destructive bg-white hover:bg-slate-50 flex w-fit items-center justify-between gap-2 rounded-md border-2 px-3 py-2 text-sm whitespace-nowrap shadow-md transition-[color,box-shadow] outline-none focus-visible:ring-4 disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,svBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 5311, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/prompt-form.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useMemo } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { usePromptStore } from \"@/lib/store\";\nimport { promptsApi, tagsApi } from \"@/lib/api\";\nimport type { Prompt } from \"@/lib/api\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport {\n  Save,\n  Plus,\n  Eye,\n  Edit,\n  Copy\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\nimport ReactMarkdown from \"react-markdown\";\nimport remarkBreaks from 'remark-breaks';\n\n// 表单验证模式\nconst promptSchema = z.object({\n  title: z.string().min(1, \"标题不能为空\").max(255, \"标题过长\"),\n  description: z.string().optional(),\n  content: z.string().min(1, \"内容不能为空\"),\n  categoryId: z.string().optional(),\n});\n\ntype PromptFormData = z.infer<typeof promptSchema>;\n\ninterface PromptFormProps {\n  prompt?: Prompt | null;\n  onSuccess?: () => void;\n}\n\nexport function PromptForm({ prompt, onSuccess }: PromptFormProps) {\n  const {\n    categories,\n    tags,\n    prompts,\n    setPrompts,\n    setTags,\n  } = usePromptStore();\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n  const [newTagName, setNewTagName] = useState(\"\");\n\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    watch,\n    formState: { errors },\n  } = useForm<PromptFormData>({\n    resolver: zodResolver(promptSchema),\n    defaultValues: {\n      title: prompt?.title || \"\",\n      description: prompt?.description || \"\",\n      content: prompt?.content || \"\",\n      categoryId: prompt?.categoryId || \"none\",\n    },\n  });\n\n  // 监听内容变化，用于预览\n  const contentValue = watch(\"content\");\n\n  // 复制代码到剪贴板\n  const handleCopyCode = async (code: string) => {\n    try {\n      await navigator.clipboard.writeText(code);\n      toast.success(\"代码已复制到剪贴板\");\n    } catch (error) {\n      console.error(\"复制失败:\", error);\n      toast.error(\"复制失败\");\n    }\n  };\n\n  // 缓存 Markdown 组件配置\n  const markdownComponents = useMemo(() => ({\n    code: ({ inline, children, ...props }: any) => {\n      if (inline) {\n        return (\n          <code\n            className=\"bg-muted px-1 py-0.5 rounded text-sm font-mono\"\n            {...props}\n          >\n            {children}\n          </code>\n        );\n      }\n      return (\n        <div className=\"relative group mb-4\">\n          <Button\n            size=\"sm\"\n            variant=\"secondary\"\n            className=\"absolute top-2 right-2 opacity-80 hover:opacity-100 transition-opacity z-10 bg-gray-700 hover:bg-gray-600 text-white border-gray-600\"\n            onClick={() => handleCopyCode(String(children))}\n          >\n            <Copy className=\"h-4 w-4 mr-1\" />\n            复制\n          </Button>\n          <pre className=\"bg-muted p-4 rounded-lg overflow-x-auto\">\n            <code className=\"font-mono text-sm\" {...props}>\n              {children}\n            </code>\n          </pre>\n        </div>\n      );\n    },\n    p: ({ children }: any) => (\n      <p className=\"mb-4 leading-relaxed\">{children}</p>\n    ),\n    ul: ({ children }: any) => (\n      <ul className=\"mb-4 pl-6 space-y-1\">{children}</ul>\n    ),\n    ol: ({ children }: any) => (\n      <ol className=\"mb-4 pl-6 space-y-1\">{children}</ol>\n    ),\n  }), []);\n\n  // 初始化选中的标签\n  useEffect(() => {\n    if (prompt?.tags) {\n      setSelectedTags(prompt.tags.map(tag => tag.id));\n    }\n  }, [prompt]);\n\n  // 处理标签选择\n  const handleTagToggle = (tagId: string) => {\n    setSelectedTags(prev => \n      prev.includes(tagId)\n        ? prev.filter(id => id !== tagId)\n        : [...prev, tagId]\n    );\n  };\n\n  // 添加新标签\n  const handleAddNewTag = async () => {\n    if (!newTagName.trim()) return;\n\n    try {\n      // 调用 API 创建新标签\n      const newTag = await tagsApi.create({\n        name: newTagName.trim(),\n        color: '#3B82F6' // 默认蓝色\n      });\n\n      // 更新本地标签列表\n      setTags([...tags, newTag]);\n\n      // 自动选中新创建的标签\n      setSelectedTags(prev => [...prev, newTag.id]);\n\n      // 清空输入框\n      setNewTagName(\"\");\n\n      toast.success(\"标签已创建并添加\");\n    } catch (error) {\n      console.error(\"创建标签失败:\", error);\n      toast.error(\"创建标签失败\");\n    }\n  };\n\n  // 提交表单\n  const onSubmit = async (data: PromptFormData) => {\n    try {\n      setIsLoading(true);\n\n      if (prompt) {\n        // 更新现有提示词\n        const updatedPrompt = await promptsApi.update(prompt.id, {\n          ...data,\n          categoryId: data.categoryId === \"none\" || !data.categoryId ? null : data.categoryId,\n          tagIds: selectedTags,\n        });\n\n        // 更新本地状态\n        const updatedPrompts = prompts.map(p =>\n          p.id === prompt.id ? updatedPrompt : p\n        );\n        setPrompts(updatedPrompts);\n\n        toast.success(\"提示词已更新\");\n      } else {\n        // 创建新提示词\n        const newPrompt = await promptsApi.create({\n          ...data,\n          categoryId: data.categoryId === \"none\" || !data.categoryId ? null : data.categoryId,\n          tagIds: selectedTags,\n        });\n\n        // 添加到本地状态\n        setPrompts([newPrompt, ...prompts]);\n\n        toast.success(\"提示词已创建\");\n      }\n\n      onSuccess?.();\n    } catch (error) {\n      console.error(\"保存失败:\", error);\n      toast.error(\"保存失败\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n      {/* 标题 */}\n      <div className=\"space-y-2\">\n        <Label htmlFor=\"title\">标题 *</Label>\n        <Input\n          id=\"title\"\n          placeholder=\"输入提示词标题...\"\n          {...register(\"title\")}\n        />\n        {errors.title && (\n          <p className=\"text-sm text-destructive\">{errors.title.message}</p>\n        )}\n      </div>\n\n      {/* 描述 */}\n      <div className=\"space-y-2\">\n        <Label htmlFor=\"description\">描述</Label>\n        <Input\n          id=\"description\"\n          placeholder=\"简短描述这个提示词的用途...\"\n          {...register(\"description\")}\n        />\n        {errors.description && (\n          <p className=\"text-sm text-destructive\">{errors.description.message}</p>\n        )}\n      </div>\n\n      {/* 分类 */}\n      <div className=\"space-y-2\">\n        <Label>分类</Label>\n        <Select\n          value={watch(\"categoryId\") || \"none\"}\n          onValueChange={(value) => setValue(\"categoryId\", value === \"none\" ? \"\" : value)}\n        >\n          <SelectTrigger>\n            <SelectValue placeholder=\"选择分类...\" />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"none\">无分类</SelectItem>\n            {categories.map((category) => (\n              <SelectItem key={category.id} value={category.id}>\n                <div className=\"flex items-center gap-2\">\n                  <div\n                    className=\"w-3 h-3 rounded-full\"\n                    style={{ backgroundColor: category.color }}\n                  />\n                  {category.name}\n                </div>\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n      </div>\n\n      {/* 标签 */}\n      <div className=\"space-y-2\">\n        <Label>标签</Label>\n        <div className=\"flex flex-wrap gap-2 mb-3\">\n          {tags.map((tag) => (\n            <Badge\n              key={tag.id}\n              variant={selectedTags.includes(tag.id) ? \"default\" : \"outline\"}\n              className=\"cursor-pointer\"\n              onClick={() => handleTagToggle(tag.id)}\n            >\n              #{tag.name}\n            </Badge>\n          ))}\n        </div>\n        \n        {/* 添加新标签 */}\n        <div className=\"flex gap-2\">\n          <Input\n            placeholder=\"添加新标签...\"\n            value={newTagName}\n            onChange={(e) => setNewTagName(e.target.value)}\n            onKeyPress={(e) => {\n              if (e.key === 'Enter') {\n                e.preventDefault();\n                handleAddNewTag();\n              }\n            }}\n          />\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleAddNewTag}\n            disabled={!newTagName.trim()}\n          >\n            <Plus className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n\n      {/* 内容 */}\n      <div className=\"space-y-2\">\n        <Label htmlFor=\"content\">内容 *</Label>\n        <Tabs defaultValue=\"edit\" className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"edit\" className=\"flex items-center gap-2\">\n              <Edit className=\"h-4 w-4\" />\n              编辑\n            </TabsTrigger>\n            <TabsTrigger value=\"preview\" className=\"flex items-center gap-2\">\n              <Eye className=\"h-4 w-4\" />\n              预览\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"edit\" className=\"mt-2\">\n            <Textarea\n              id=\"content\"\n              placeholder=\"输入提示词内容，支持 Markdown 格式...\"\n              rows={12}\n              {...register(\"content\")}\n              className=\"font-mono\"\n            />\n          </TabsContent>\n\n          <TabsContent value=\"preview\" className=\"mt-2\">\n            <div className=\"min-h-[300px] p-4 border rounded-md bg-muted/50\">\n              {contentValue ? (\n                <div className=\"prose prose-sm max-w-none dark:prose-invert\">\n                  <ReactMarkdown\n                    components={markdownComponents}\n                    remarkPlugins={[remarkBreaks]}\n                  >\n                    {contentValue}\n                  </ReactMarkdown>\n                </div>\n              ) : (\n                <div className=\"text-muted-foreground text-center py-12\">\n                  在编辑器中输入内容以查看预览\n                </div>\n              )}\n            </div>\n          </TabsContent>\n        </Tabs>\n\n        {errors.content && (\n          <p className=\"text-sm text-destructive\">{errors.content.message}</p>\n        )}\n        <p className=\"text-xs text-muted-foreground\">\n          支持 Markdown 格式，包括代码块、列表、链接等。可以在预览标签页查看渲染效果。\n        </p>\n      </div>\n\n      {/* 操作按钮 */}\n      <div className=\"flex justify-end gap-3 pt-4 border-t\">\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={onSuccess}\n          disabled={isLoading}\n        >\n          取消\n        </Button>\n        <Button type=\"submit\" disabled={isLoading}>\n          {isLoading ? (\n            \"保存中...\"\n          ) : (\n            <>\n              <Save className=\"h-4 w-4 mr-2\" />\n              {prompt ? \"更新\" : \"创建\"}\n            </>\n          )}\n        </Button>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AACA;;;AA/BA;;;;;;;;;;;;;;;;;;AAiCA,SAAS;AACT,MAAM,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC5C,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AASO,SAAS,WAAW,KAAsC;QAAtC,EAAE,MAAM,EAAE,SAAS,EAAmB,GAAtC;;IACzB,MAAM,EACJ,UAAU,EACV,IAAI,EACJ,OAAO,EACP,UAAU,EACV,OAAO,EACR,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkB;QAC1B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO,CAAA,mBAAA,6BAAA,OAAQ,KAAK,KAAI;YACxB,aAAa,CAAA,mBAAA,6BAAA,OAAQ,WAAW,KAAI;YACpC,SAAS,CAAA,mBAAA,6BAAA,OAAQ,OAAO,KAAI;YAC5B,YAAY,CAAA,mBAAA,6BAAA,OAAQ,UAAU,KAAI;QACpC;IACF;IAEA,cAAc;IACd,MAAM,eAAe,MAAM;IAE3B,WAAW;IACX,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,mBAAmB;IACnB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE,IAAM,CAAC;gBACxC,IAAI;8DAAE;4BAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAY;wBACxC,IAAI,QAAQ;4BACV,qBACE,6LAAC;gCACC,WAAU;gCACT,GAAG,KAAK;0CAER;;;;;;wBAGP;wBACA,qBACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,OAAO;kFAAE,IAAM,eAAe,OAAO;;;sDAErC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;wCAAqB,GAAG,KAAK;kDAC1C;;;;;;;;;;;;;;;;;oBAKX;;gBACA,CAAC;8DAAE;4BAAC,EAAE,QAAQ,EAAO;6CACnB,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;gBAEvC,EAAE;8DAAE;4BAAC,EAAE,QAAQ,EAAO;6CACpB,6LAAC;4BAAG,WAAU;sCAAuB;;;;;;;;gBAEvC,EAAE;8DAAE;4BAAC,EAAE,QAAQ,EAAO;6CACpB,6LAAC;4BAAG,WAAU;sCAAuB;;;;;;;;YAEzC,CAAC;iDAAG,EAAE;IAEN,WAAW;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE;gBAChB,gBAAgB,OAAO,IAAI,CAAC,GAAG;4CAAC,CAAA,MAAO,IAAI,EAAE;;YAC/C;QACF;+BAAG;QAAC;KAAO;IAEX,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,gBAAgB,CAAA,OACd,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,SACzB;mBAAI;gBAAM;aAAM;IAExB;IAEA,QAAQ;IACR,MAAM,kBAAkB;QACtB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,IAAI;YACF,eAAe;YACf,MAAM,SAAS,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;gBAClC,MAAM,WAAW,IAAI;gBACrB,OAAO,UAAU,OAAO;YAC1B;YAEA,WAAW;YACX,QAAQ;mBAAI;gBAAM;aAAO;YAEzB,aAAa;YACb,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM,OAAO,EAAE;iBAAC;YAE5C,QAAQ;YACR,cAAc;YAEd,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,OAAO;IACP,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa;YAEb,IAAI,QAAQ;gBACV,UAAU;gBACV,MAAM,gBAAgB,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;oBACvD,GAAG,IAAI;oBACP,YAAY,KAAK,UAAU,KAAK,UAAU,CAAC,KAAK,UAAU,GAAG,OAAO,KAAK,UAAU;oBACnF,QAAQ;gBACV;gBAEA,SAAS;gBACT,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,IACjC,EAAE,EAAE,KAAK,OAAO,EAAE,GAAG,gBAAgB;gBAEvC,WAAW;gBAEX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,SAAS;gBACT,MAAM,YAAY,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;oBACxC,GAAG,IAAI;oBACP,YAAY,KAAK,UAAU,KAAK,UAAU,CAAC,KAAK,UAAU,GAAG,OAAO,KAAK,UAAU;oBACnF,QAAQ;gBACV;gBAEA,UAAU;gBACV,WAAW;oBAAC;uBAAc;iBAAQ;gBAElC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA,sBAAA,gCAAA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU,aAAa;QAAW,WAAU;;0BAEhD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAQ;;;;;;kCACvB,6LAAC,oIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,aAAY;wBACX,GAAG,SAAS,QAAQ;;;;;;oBAEtB,OAAO,KAAK,kBACX,6LAAC;wBAAE,WAAU;kCAA4B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0BAKjE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAc;;;;;;kCAC7B,6LAAC,oIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,aAAY;wBACX,GAAG,SAAS,cAAc;;;;;;oBAE5B,OAAO,WAAW,kBACjB,6LAAC;wBAAE,WAAU;kCAA4B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;0BAKvE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;kCAAC;;;;;;kCACP,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,MAAM,iBAAiB;wBAC9B,eAAe,CAAC,QAAU,SAAS,cAAc,UAAU,SAAS,KAAK;;0CAEzE,6LAAC,qIAAA,CAAA,gBAAa;0CACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAO;;;;;;oCACxB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,aAAU;4CAAmB,OAAO,SAAS,EAAE;sDAC9C,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,SAAS,KAAK;wDAAC;;;;;;oDAE1C,SAAS,IAAI;;;;;;;2CAND,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;0BAepC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;kCAAC;;;;;;kCACP,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC,oIAAA,CAAA,QAAK;gCAEJ,SAAS,aAAa,QAAQ,CAAC,IAAI,EAAE,IAAI,YAAY;gCACrD,WAAU;gCACV,SAAS,IAAM,gBAAgB,IAAI,EAAE;;oCACtC;oCACG,IAAI,IAAI;;+BALL,IAAI,EAAE;;;;;;;;;;kCAWjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,YAAY,CAAC;oCACX,IAAI,EAAE,GAAG,KAAK,SAAS;wCACrB,EAAE,cAAc;wCAChB;oCACF;gCACF;;;;;;0CAEF,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC,WAAW,IAAI;0CAE1B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAU;;;;;;kCACzB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAO,WAAU;;0CAClC,6LAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAO,WAAU;;0DAClC,6LAAC,8MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG9B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAU,WAAU;;0DACrC,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAK/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAO,WAAU;0CAClC,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,MAAM;oCACL,GAAG,SAAS,UAAU;oCACvB,WAAU;;;;;;;;;;;0CAId,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;0CACrC,cAAA,6LAAC;oCAAI,WAAU;8CACZ,6BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2LAAA,CAAA,UAAa;4CACZ,YAAY;4CACZ,eAAe;gDAAC,mJAAA,CAAA,UAAY;6CAAC;sDAE5B;;;;;;;;;;6DAIL,6LAAC;wCAAI,WAAU;kDAA0C;;;;;;;;;;;;;;;;;;;;;;oBAQhE,OAAO,OAAO,kBACb,6LAAC;wBAAE,WAAU;kCAA4B,OAAO,OAAO,CAAC,OAAO;;;;;;kCAEjE,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAM/C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU;kCACX;;;;;;kCAGD,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,UAAU;kCAC7B,YACC,yBAEA;;8CACE,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCACf,SAAS,OAAO;;;;;;;;;;;;;;;;;;;;AAO/B;GAxVgB;;QAOV,sHAAA,CAAA,iBAAc;QAYd,iKAAA,CAAA,UAAO;;;KAnBG", "debugId": null}}, {"offset": {"line": 6025, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/prompt-dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useMemo, memo } from \"react\";\nimport { usePromptStore } from \"@/lib/store\";\nimport { promptsApi } from \"@/lib/api\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n  DialogDescription,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { Separator } from \"@/components/ui/separator\";\nimport {\n  Copy,\n  Edit,\n  TrendingUp,\n  Calendar,\n  Eye,\n  Hash,\n  Folder,\n  Download,\n  Share,\n  MoreHorizontal,\n  Code,\n  FileText,\n  ExternalLink,\n  Clock,\n  User,\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\nimport { PromptForm } from \"./prompt-form\";\nimport ReactMarkdown from 'react-markdown';\nimport { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';\nimport { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';\nimport remarkBreaks from 'remark-breaks';\n\nconst PromptDialogComponent = () => {\n  const {\n    isPromptDialogOpen,\n    selectedPrompt,\n    isEditMode,\n    closePromptDialog,\n    openPromptDialog,\n    setPrompts,\n    prompts,\n  } = usePromptStore();\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(\"content\");\n\n  // 缓存 Markdown 组件配置以提高性能\n  const markdownComponents = useMemo(() => ({\n    code: ({ node, inline, className, children, ...props }: any) => {\n      const match = /language-(\\w+)/.exec(className || '');\n      const language = match ? match[1] : '';\n\n      if (inline) {\n        return (\n          <code\n            className=\"bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono border border-gray-200 dark:border-gray-600 text-gray-800 dark:text-gray-200\"\n            {...props}\n          >\n            {children}\n          </code>\n        );\n      }\n\n      return (\n        <div className=\"relative group mb-4\">\n          {/* 代码块容器 */}\n          <div className=\"relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 bg-[#1e1e1e]\">\n            {/* 顶部工具栏 */}\n            <div className=\"flex items-center justify-between px-4 py-2 bg-gray-800 border-b border-gray-700\">\n              {/* 语言标识 */}\n              <div className=\"flex items-center gap-2\">\n                {language && (\n                  <span className=\"text-xs px-2 py-1 bg-gray-700 text-gray-300 rounded font-mono\">\n                    {language}\n                  </span>\n                )}\n              </div>\n\n              {/* 复制按钮 */}\n              <Button\n                size=\"sm\"\n                variant=\"ghost\"\n                className=\"h-7 px-2 text-gray-300 hover:text-white hover:bg-gray-700 transition-colors\"\n                onClick={() => handleCopyCode(String(children))}\n              >\n                <Copy className=\"h-3.5 w-3.5 mr-1.5\" />\n                <span className=\"text-xs\">复制</span>\n              </Button>\n            </div>\n\n            {/* 代码内容 */}\n            <SyntaxHighlighter\n              style={oneDark}\n              language={language || 'text'}\n              PreTag=\"div\"\n              className=\"!mt-0 !mb-0 overflow-x-auto\"\n              customStyle={{\n                padding: '1.5rem',\n                fontSize: '14px',\n                lineHeight: '1.5',\n                margin: 0,\n                background: '#1e1e1e'\n              }}\n              {...props}\n            >\n              {String(children).replace(/\\n$/, '')}\n            </SyntaxHighlighter>\n          </div>\n        </div>\n      );\n    },\n    p: ({ children }: any) => (\n      <p className=\"mb-4 leading-relaxed text-base\">{children}</p>\n    ),\n    h1: ({ children }: any) => (\n      <h1 className=\"text-2xl font-bold mb-4 mt-6 first:mt-0\">{children}</h1>\n    ),\n    h2: ({ children }: any) => (\n      <h2 className=\"text-xl font-semibold mb-3 mt-5\">{children}</h2>\n    ),\n    h3: ({ children }: any) => (\n      <h3 className=\"text-lg font-medium mb-2 mt-4\">{children}</h3>\n    ),\n    ul: ({ children }: any) => (\n      <ul className=\"mb-4 pl-6 space-y-2\">{children}</ul>\n    ),\n    ol: ({ children }: any) => (\n      <ol className=\"mb-4 pl-6 space-y-2\">{children}</ol>\n    ),\n    blockquote: ({ children }: any) => (\n      <blockquote className=\"border-l-4 border-primary pl-4 italic my-4 text-muted-foreground\">\n        {children}\n      </blockquote>\n    ),\n  }), []);\n\n  // 缓存渲染的 Markdown 内容\n  const renderedMarkdown = useMemo(() => {\n    if (!selectedPrompt?.content) return null;\n\n    return (\n      <ReactMarkdown\n        components={markdownComponents}\n        remarkPlugins={[remarkBreaks]}\n      >\n        {selectedPrompt.content}\n      </ReactMarkdown>\n    );\n  }, [selectedPrompt?.content, markdownComponents]);\n\n  // 复制提示词内容\n  const handleCopy = async () => {\n    if (!selectedPrompt) return;\n\n    try {\n      await navigator.clipboard.writeText(selectedPrompt.content);\n\n      // 增加使用次数\n      await promptsApi.incrementUsage(selectedPrompt.id);\n\n      // 更新本地状态\n      const updatedPrompts = prompts.map(p =>\n        p.id === selectedPrompt.id\n          ? { ...p, usageCount: p.usageCount + 1 }\n          : p\n      );\n      setPrompts(updatedPrompts);\n\n      toast.success(\"已复制到剪贴板\");\n    } catch (error) {\n      console.error(\"复制失败:\", error);\n      toast.error(\"复制失败\");\n    }\n  };\n\n  // 复制代码块\n  const handleCopyCode = async (code: string) => {\n    try {\n      await navigator.clipboard.writeText(code);\n      toast.success(\"代码已复制到剪贴板\");\n    } catch (error) {\n      console.error(\"复制失败:\", error);\n      toast.error(\"复制失败\");\n    }\n  };\n\n  // 格式化日期\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // 导出提示词\n  const handleExport = () => {\n    if (!selectedPrompt) return;\n\n    const exportData = {\n      title: selectedPrompt.title,\n      description: selectedPrompt.description,\n      content: selectedPrompt.content,\n      category: selectedPrompt.category?.name,\n      tags: selectedPrompt.tags?.map(tag => tag.name),\n      created_at: selectedPrompt.createdAt,\n      updated_at: selectedPrompt.updatedAt,\n    };\n\n    const blob = new Blob([JSON.stringify(exportData, null, 2)], {\n      type: 'application/json'\n    });\n\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${selectedPrompt.title}.json`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n\n    toast.success(\"提示词已导出\");\n  };\n\n  // 只有在明确的编辑模式或新建模式下才渲染表单\n  // 避免在关闭过程中的中间状态触发新建模式\n  if (isEditMode || (!selectedPrompt && isPromptDialogOpen)) {\n    return (\n      <Dialog open={isPromptDialogOpen} onOpenChange={(open) => {\n        if (!open) {\n          closePromptDialog();\n        }\n      }}>\n        <DialogContent className=\"max-w-[50vw] max-h-[90vh] w-[50vw] overflow-hidden flex flex-col bg-slate-50 border-4 border-slate-800 shadow-2xl animate-scale-in ring-8 ring-primary/20\">\n          <DialogHeader>\n            <DialogTitle className=\"text-xl font-bold text-foreground\">\n              {selectedPrompt ? \"编辑提示词\" : \"新建提示词\"}\n            </DialogTitle>\n            <DialogDescription className=\"text-muted-foreground\">\n              {selectedPrompt ? \"修改提示词的内容、分类和标签\" : \"创建一个新的提示词\"}\n            </DialogDescription>\n          </DialogHeader>\n          <div className=\"flex-1 overflow-auto\">\n            <PromptForm\n              prompt={selectedPrompt}\n              onSuccess={closePromptDialog}\n            />\n          </div>\n        </DialogContent>\n      </Dialog>\n    );\n  }\n\n  // 如果没有选中的提示词，不渲染详情模式\n  if (!selectedPrompt) {\n    return null;\n  }\n\n  return (\n    <Dialog open={isPromptDialogOpen} onOpenChange={(open) => {\n      if (!open) {\n        closePromptDialog();\n      }\n    }}>\n      <DialogContent className=\"max-w-[50vw] max-h-[95vh] w-[50vw] h-[95vh] overflow-hidden flex flex-col bg-white border-2 border-slate-300 shadow-xl animate-scale-in\"\n        style={{ maxHeight: '95vh', height: '95vh' }}>\n\n        {/* 头部区域 */}\n        <DialogHeader className=\"flex-shrink-0 pb-0\">\n          <DialogTitle className=\"text-lg font-medium text-slate-600 mb-4\">\n            提示词详情\n          </DialogTitle>\n          <DialogDescription className=\"sr-only\">\n            查看和管理提示词的详细内容、标签和元数据信息\n          </DialogDescription>\n\n          {/* 提示词标题 */}\n          <div className=\"mb-4\">\n            <h2 className=\"text-2xl font-bold text-slate-900 mb-3\">\n              {selectedPrompt.title}\n            </h2>\n\n            {/* 元数据信息行 */}\n            <div className=\"flex items-center gap-6 text-sm text-slate-600\">\n              {/* 分类 */}\n              {selectedPrompt.category && (\n                <div className=\"flex items-center gap-1\">\n                  <div\n                    className=\"w-3 h-3 rounded\"\n                    style={{ backgroundColor: selectedPrompt.category.color }}\n                  />\n                  <span>{selectedPrompt.category.name}</span>\n                </div>\n              )}\n\n              {/* 使用次数 */}\n              <div className=\"flex items-center gap-1\">\n                <span className=\"text-slate-500\">使用</span>\n                <span className=\"font-medium\">{selectedPrompt.usageCount} 次</span>\n              </div>\n\n              {/* 创建时间 */}\n              <div className=\"flex items-center gap-1\">\n                <span className=\"text-slate-500\">创建于</span>\n                <span>{formatDate(selectedPrompt.createdAt)}</span>\n              </div>\n\n              {/* 更新时间 */}\n              <div className=\"flex items-center gap-1\">\n                <span className=\"text-slate-500\">更新于</span>\n                <span>{formatDate(selectedPrompt.updatedAt)}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* 标签区域 */}\n          {selectedPrompt.tags && selectedPrompt.tags.length > 0 && (\n            <div className=\"mb-6\">\n              <h3 className=\"text-sm font-medium text-slate-700 mb-2\">标签</h3>\n              <div className=\"flex flex-wrap gap-2\">\n                {selectedPrompt.tags.map((tag) => (\n                  <span\n                    key={tag.id}\n                    className=\"px-2 py-1 text-sm bg-slate-100 text-slate-700 rounded\"\n                  >\n                    #{tag.name}\n                  </span>\n                ))}\n              </div>\n            </div>\n          )}\n        </DialogHeader>\n\n        {/* 内容区域 */}\n        <div className=\"flex-1 overflow-hidden flex flex-col\">\n          <div className=\"flex-shrink-0 mb-4\">\n            <h3 className=\"text-sm font-medium text-slate-700 mb-3\">提示词内容</h3>\n          </div>\n          <div className=\"bg-slate-50 rounded-lg p-4 border border-slate-200 flex-1 flex flex-col\">\n            <Tabs value={activeTab} onValueChange={setActiveTab} className=\"h-full flex flex-col\">\n                <TabsList className=\"grid w-full grid-cols-2 mb-4 bg-white\">\n                  <TabsTrigger value=\"content\" className=\"gap-2 data-[state=active]:bg-primary data-[state=active]:text-white\">\n                    <FileText className=\"h-4 w-4\" />\n                    Markdown 预览\n                  </TabsTrigger>\n                  <TabsTrigger value=\"raw\" className=\"gap-2 data-[state=active]:bg-primary data-[state=active]:text-white\">\n                    <Code className=\"h-4 w-4\" />\n                    原始文本\n                  </TabsTrigger>\n                </TabsList>\n\n                <TabsContent value=\"content\" className=\"flex-1 overflow-hidden mt-0\">\n                  <ScrollArea className=\"h-full\" style={{ height: 'calc(95vh - 300px)' }}>\n                    <div className=\"prose prose-sm max-w-none p-4 bg-white rounded border\">\n                      {renderedMarkdown}\n                    </div>\n                  </ScrollArea>\n                </TabsContent>\n\n                <TabsContent value=\"raw\" className=\"flex-1 overflow-hidden mt-0\">\n                  <ScrollArea className=\"h-full\" style={{ height: 'calc(95vh - 300px)' }}>\n                    <div className=\"bg-slate-800 text-slate-100 p-4 rounded font-mono text-sm leading-relaxed whitespace-pre-wrap\">\n                      {selectedPrompt.content}\n                    </div>\n                  </ScrollArea>\n                </TabsContent>\n              </Tabs>\n            </div>\n        </div>\n\n        {/* 底部操作按钮 */}\n        <div className=\"flex items-center justify-between pt-4 border-t border-slate-200\">\n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => {\n                // 直接切换到编辑模式，避免状态冲突\n                if (selectedPrompt) {\n                  openPromptDialog(selectedPrompt, true);\n                }\n              }}\n              className=\"gap-2\"\n            >\n              <Edit className=\"h-4 w-4\" />\n              编辑\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleExport}\n              className=\"gap-2 text-red-600 border-red-200 hover:bg-red-50\"\n            >\n              <Download className=\"h-4 w-4\" />\n              删除\n            </Button>\n          </div>\n\n          <Button\n            onClick={handleCopy}\n            disabled={isLoading}\n            className=\"gap-2 bg-green-600 hover:bg-green-700 text-white\"\n          >\n            <Copy className=\"h-4 w-4\" />\n            复制内容\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\n// 使用 memo 优化性能，避免不必要的重新渲染\nexport const PromptDialog = memo(PromptDialogComponent);\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAOA;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;AACA;AACA;;;AAvCA;;;;;;;;;;;;;;;AAyCA,MAAM,wBAAwB;;IAC5B,MAAM,EACJ,kBAAkB,EAClB,cAAc,EACd,UAAU,EACV,iBAAiB,EACjB,gBAAgB,EAChB,UAAU,EACV,OAAO,EACR,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wBAAwB;IACxB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6DAAE,IAAM,CAAC;gBACxC,IAAI;yEAAE;4BAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY;wBACzD,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;wBACjD,MAAM,WAAW,QAAQ,KAAK,CAAC,EAAE,GAAG;wBAEpC,IAAI,QAAQ;4BACV,qBACE,6LAAC;gCACC,WAAU;gCACT,GAAG,KAAK;0CAER;;;;;;wBAGP;wBAEA,qBACE,6LAAC;4BAAI,WAAU;sCAEb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,0BACC,6LAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;0DAMP,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,OAAO;yGAAE,IAAM,eAAe,OAAO;;;kEAErC,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;kDAK9B,6LAAC,6MAAA,CAAA,QAAiB;wCAChB,OAAO,wOAAA,CAAA,UAAO;wCACd,UAAU,YAAY;wCACtB,QAAO;wCACP,WAAU;wCACV,aAAa;4CACX,SAAS;4CACT,UAAU;4CACV,YAAY;4CACZ,QAAQ;4CACR,YAAY;wCACd;wCACC,GAAG,KAAK;kDAER,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;oBAK3C;;gBACA,CAAC;yEAAE;4BAAC,EAAE,QAAQ,EAAO;6CACnB,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;;;gBAEjD,EAAE;yEAAE;4BAAC,EAAE,QAAQ,EAAO;6CACpB,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;;;gBAE3D,EAAE;yEAAE;4BAAC,EAAE,QAAQ,EAAO;6CACpB,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;;;gBAEnD,EAAE;yEAAE;4BAAC,EAAE,QAAQ,EAAO;6CACpB,6LAAC;4BAAG,WAAU;sCAAiC;;;;;;;;gBAEjD,EAAE;yEAAE;4BAAC,EAAE,QAAQ,EAAO;6CACpB,6LAAC;4BAAG,WAAU;sCAAuB;;;;;;;;gBAEvC,EAAE;yEAAE;4BAAC,EAAE,QAAQ,EAAO;6CACpB,6LAAC;4BAAG,WAAU;sCAAuB;;;;;;;;gBAEvC,UAAU;yEAAE;4BAAC,EAAE,QAAQ,EAAO;6CAC5B,6LAAC;4BAAW,WAAU;sCACnB;;;;;;;;YAGP,CAAC;4DAAG,EAAE;IAEN,oBAAoB;IACpB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2DAAE;YAC/B,IAAI,EAAC,2BAAA,qCAAA,eAAgB,OAAO,GAAE,OAAO;YAErC,qBACE,6LAAC,2LAAA,CAAA,UAAa;gBACZ,YAAY;gBACZ,eAAe;oBAAC,mJAAA,CAAA,UAAY;iBAAC;0BAE5B,eAAe,OAAO;;;;;;QAG7B;0DAAG;QAAC,2BAAA,qCAAA,eAAgB,OAAO;QAAE;KAAmB;IAEhD,UAAU;IACV,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,eAAe,OAAO;YAE1D,SAAS;YACT,MAAM,oHAAA,CAAA,aAAU,CAAC,cAAc,CAAC,eAAe,EAAE;YAEjD,SAAS;YACT,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,IACjC,EAAE,EAAE,KAAK,eAAe,EAAE,GACtB;oBAAE,GAAG,CAAC;oBAAE,YAAY,EAAE,UAAU,GAAG;gBAAE,IACrC;YAEN,WAAW;YAEX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,QAAQ;IACR,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,cAAc,CAAC,SAAS;YAClD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,QAAQ;IACR,MAAM,eAAe;YAOP,0BACJ;QAPR,IAAI,CAAC,gBAAgB;QAErB,MAAM,aAAa;YACjB,OAAO,eAAe,KAAK;YAC3B,aAAa,eAAe,WAAW;YACvC,SAAS,eAAe,OAAO;YAC/B,QAAQ,GAAE,2BAAA,eAAe,QAAQ,cAAvB,+CAAA,yBAAyB,IAAI;YACvC,IAAI,GAAE,uBAAA,eAAe,IAAI,cAAnB,2CAAA,qBAAqB,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;YAC9C,YAAY,eAAe,SAAS;YACpC,YAAY,eAAe,SAAS;QACtC;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC,KAAK,SAAS,CAAC,YAAY,MAAM;SAAG,EAAE;YAC3D,MAAM;QACR;QAEA,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,AAAC,GAAuB,OAArB,eAAe,KAAK,EAAC;QACrC,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;QAEpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,wBAAwB;IACxB,sBAAsB;IACtB,IAAI,cAAe,CAAC,kBAAkB,oBAAqB;QACzD,qBACE,6LAAC,qIAAA,CAAA,SAAM;YAAC,MAAM;YAAoB,cAAc,CAAC;gBAC/C,IAAI,CAAC,MAAM;oBACT;gBACF;YACF;sBACE,cAAA,6LAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,6LAAC,qIAAA,CAAA,eAAY;;0CACX,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,iBAAiB,UAAU;;;;;;0CAE9B,6LAAC,qIAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC1B,iBAAiB,mBAAmB;;;;;;;;;;;;kCAGzC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uIAAA,CAAA,aAAU;4BACT,QAAQ;4BACR,WAAW;;;;;;;;;;;;;;;;;;;;;;IAMvB;IAEA,qBAAqB;IACrB,IAAI,CAAC,gBAAgB;QACnB,OAAO;IACT;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAoB,cAAc,CAAC;YAC/C,IAAI,CAAC,MAAM;gBACT;YACF;QACF;kBACE,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;YACvB,OAAO;gBAAE,WAAW;gBAAQ,QAAQ;YAAO;;8BAG3C,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;sCAA0C;;;;;;sCAGjE,6LAAC,qIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAU;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,eAAe,KAAK;;;;;;8CAIvB,6LAAC;oCAAI,WAAU;;wCAEZ,eAAe,QAAQ,kBACtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,eAAe,QAAQ,CAAC,KAAK;oDAAC;;;;;;8DAE1D,6LAAC;8DAAM,eAAe,QAAQ,CAAC,IAAI;;;;;;;;;;;;sDAKvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,6LAAC;oDAAK,WAAU;;wDAAe,eAAe,UAAU;wDAAC;;;;;;;;;;;;;sDAI3D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,6LAAC;8DAAM,WAAW,eAAe,SAAS;;;;;;;;;;;;sDAI5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,6LAAC;8DAAM,WAAW,eAAe,SAAS;;;;;;;;;;;;;;;;;;;;;;;;wBAM/C,eAAe,IAAI,IAAI,eAAe,IAAI,CAAC,MAAM,GAAG,mBACnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,6LAAC;oCAAI,WAAU;8CACZ,eAAe,IAAI,CAAC,GAAG,CAAC,CAAC,oBACxB,6LAAC;4CAEC,WAAU;;gDACX;gDACG,IAAI,IAAI;;2CAHL,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;8BAYvB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;;;;;;sCAE1D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,OAAO;gCAAW,eAAe;gCAAc,WAAU;;kDAC3D,6LAAC,mIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAU,WAAU;;kEACrC,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAM,WAAU;;kEACjC,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;kDAKhC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAU,WAAU;kDACrC,cAAA,6LAAC,6IAAA,CAAA,aAAU;4CAAC,WAAU;4CAAS,OAAO;gDAAE,QAAQ;4CAAqB;sDACnE,cAAA,6LAAC;gDAAI,WAAU;0DACZ;;;;;;;;;;;;;;;;kDAKP,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAM,WAAU;kDACjC,cAAA,6LAAC,6IAAA,CAAA,aAAU;4CAAC,WAAU;4CAAS,OAAO;gDAAE,QAAQ;4CAAqB;sDACnE,cAAA,6LAAC;gDAAI,WAAU;0DACZ,eAAe,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP,mBAAmB;wCACnB,IAAI,gBAAgB;4CAClB,iBAAiB,gBAAgB;wCACnC;oCACF;oCACA,WAAU;;sDAEV,6LAAC,8MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG9B,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAKpC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GA7XM;;QASA,sHAAA,CAAA,iBAAc;;;KATd;AAgYC,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 6881, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/category-dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { usePromptStore, useCategoryStore } from \"@/lib/store\";\nimport { categoriesApi } from \"@/lib/api\";\nimport type { Category } from \"@/lib/api\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n  DialogDescription,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { \n  Save, \n  Palette,\n  Folder\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\nimport { PRESET_ICONS, getIconComponent } from \"@/lib/icons\";\n\n// 表单验证模式\nconst categorySchema = z.object({\n  name: z.string().min(1, \"分类名称不能为空\").max(100, \"分类名称过长\"),\n  color: z.string().regex(/^#[0-9A-F]{6}$/i, \"请选择有效的颜色\"),\n  icon: z.string().default(\"folder\"),\n  parentId: z.string().optional(),\n  sortOrder: z.number().default(0),\n});\n\ntype CategoryFormData = z.infer<typeof categorySchema>;\n\n// 预设颜色\nconst PRESET_COLORS = [\n  \"#3B82F6\", // 蓝色\n  \"#10B981\", // 绿色\n  \"#F59E0B\", // 黄色\n  \"#EF4444\", // 红色\n  \"#8B5CF6\", // 紫色\n  \"#F97316\", // 橙色\n  \"#06B6D4\", // 青色\n  \"#84CC16\", // 青绿色\n  \"#EC4899\", // 粉色\n  \"#6B7280\", // 灰色\n];\n\n\n\nexport function CategoryDialog() {\n  const {\n    categories,\n    setCategories,\n    prompts,\n    setPrompts,\n  } = usePromptStore();\n\n  const {\n    isCategoryDialogOpen,\n    selectedCategoryForEdit,\n    closeCategoryDialog,\n  } = useCategoryStore();\n\n  const [isLoading, setIsLoading] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    watch,\n    reset,\n    formState: { errors },\n  } = useForm<CategoryFormData>({\n    resolver: zodResolver(categorySchema),\n    defaultValues: {\n      name: \"\",\n      color: PRESET_COLORS[0],\n      icon: \"folder\",\n      parentId: \"none\",\n      sortOrder: 0,\n    },\n  });\n\n  // 当选中的分类改变时，更新表单\n  useEffect(() => {\n    if (selectedCategoryForEdit) {\n      reset({\n        name: selectedCategoryForEdit.name,\n        color: selectedCategoryForEdit.color,\n        icon: selectedCategoryForEdit.icon,\n        parentId: selectedCategoryForEdit.parentId || \"none\",\n        sortOrder: selectedCategoryForEdit.sortOrder,\n      });\n    } else {\n      reset({\n        name: \"\",\n        color: PRESET_COLORS[0],\n        icon: \"folder\",\n        parentId: \"none\",\n        sortOrder: categories.length,\n      });\n    }\n  }, [selectedCategoryForEdit, reset, categories.length]);\n\n  // 提交表单\n  const onSubmit = async (data: CategoryFormData) => {\n    try {\n      setIsLoading(true);\n\n      const categoryData = {\n        ...data,\n        parentId: data.parentId === \"none\" || !data.parentId ? null : data.parentId,\n      };\n\n      if (selectedCategoryForEdit) {\n        // 更新现有分类\n        const updatedCategory = await categoriesApi.update(\n          selectedCategoryForEdit.id,\n          categoryData\n        );\n\n        // 更新本地状态\n        const updatedCategories = categories.map(cat =>\n          cat.id === selectedCategoryForEdit.id ? updatedCategory : cat\n        );\n        setCategories(updatedCategories);\n\n        // 同时更新相关提示词的分类信息\n        const updatedPrompts = prompts.map(prompt =>\n          prompt.categoryId === selectedCategoryForEdit.id\n            ? { ...prompt, category: updatedCategory }\n            : prompt\n        );\n        setPrompts(updatedPrompts);\n\n        toast.success(\"分类已更新\");\n      } else {\n        // 创建新分类\n        const newCategory = await categoriesApi.create(categoryData);\n\n        // 添加到本地状态\n        setCategories([...categories, newCategory]);\n\n        toast.success(\"分类已创建\");\n      }\n\n      closeCategoryDialog();\n    } catch (error) {\n      console.error(\"保存分类失败:\", error);\n      toast.error(\"保存分类失败\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 获取可选的父分类（排除自己和子分类）\n  const getAvailableParentCategories = () => {\n    if (!selectedCategoryForEdit) return categories;\n    \n    // 排除自己\n    return categories.filter(cat => cat.id !== selectedCategoryForEdit.id);\n  };\n\n  return (\n    <Dialog open={isCategoryDialogOpen} onOpenChange={closeCategoryDialog}>\n      <DialogContent className=\"max-w-md bg-slate-50 border-4 border-slate-800 shadow-2xl ring-8 ring-primary/20\">\n        <DialogHeader>\n          <DialogTitle>\n            {selectedCategoryForEdit ? \"编辑分类\" : \"新建分类\"}\n          </DialogTitle>\n          <DialogDescription>\n            {selectedCategoryForEdit ? \"修改分类的名称、颜色和图标\" : \"创建一个新的分类来组织你的提示词\"}\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          {/* 分类名称 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"name\">分类名称 *</Label>\n            <Input\n              id=\"name\"\n              placeholder=\"输入分类名称...\"\n              {...register(\"name\")}\n            />\n            {errors.name && (\n              <p className=\"text-sm text-destructive\">{errors.name.message}</p>\n            )}\n          </div>\n\n          {/* 颜色选择 */}\n          <div className=\"space-y-2\">\n            <Label>颜色</Label>\n            <div className=\"flex flex-wrap gap-2\">\n              {PRESET_COLORS.map((color) => (\n                <button\n                  key={color}\n                  type=\"button\"\n                  className={`w-8 h-8 rounded-full border-2 transition-all ${\n                    watch(\"color\") === color \n                      ? \"border-foreground scale-110\" \n                      : \"border-muted hover:scale-105\"\n                  }`}\n                  style={{ backgroundColor: color }}\n                  onClick={() => setValue(\"color\", color)}\n                />\n              ))}\n            </div>\n            <Input\n              type=\"color\"\n              value={watch(\"color\")}\n              onChange={(e) => setValue(\"color\", e.target.value)}\n              className=\"w-20 h-10\"\n            />\n          </div>\n\n          {/* 图标选择 */}\n          <div className=\"space-y-2\">\n            <Label>图标</Label>\n            <Select\n              value={watch(\"icon\")}\n              onValueChange={(value) => setValue(\"icon\", value)}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                {PRESET_ICONS.map((icon) => (\n                  <SelectItem key={icon.value} value={icon.value}>\n                    <div className=\"flex items-center gap-2\">\n                      <span>{icon.icon}</span>\n                      <span>{icon.label}</span>\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* 父分类 */}\n          <div className=\"space-y-2\">\n            <Label>父分类</Label>\n            <Select\n              value={watch(\"parentId\") || \"none\"}\n              onValueChange={(value) => setValue(\"parentId\", value === \"none\" ? \"\" : value)}\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"选择父分类（可选）\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"none\">无父分类</SelectItem>\n                {getAvailableParentCategories().map((category) => (\n                  <SelectItem key={category.id} value={category.id}>\n                    <div className=\"flex items-center gap-2\">\n                      <div\n                        className=\"w-3 h-3 rounded-full\"\n                        style={{ backgroundColor: category.color }}\n                      />\n                      {category.name}\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* 预览 */}\n          <div className=\"space-y-2\">\n            <Label>预览</Label>\n            <div className=\"flex items-center gap-2 p-3 border rounded-lg\">\n              {(() => {\n                const IconComponent = getIconComponent(watch(\"icon\"));\n                return <IconComponent\n                  className=\"h-4 w-4\"\n                  style={{ color: watch(\"color\") }}\n                />;\n              })()}\n              <span>{watch(\"name\") || \"分类名称\"}</span>\n            </div>\n          </div>\n\n          {/* 操作按钮 */}\n          <div className=\"flex justify-end gap-3 pt-4 border-t\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={closeCategoryDialog}\n              disabled={isLoading}\n            >\n              取消\n            </Button>\n            <Button type=\"submit\" disabled={isLoading}>\n              {isLoading ? (\n                \"保存中...\"\n              ) : (\n                <>\n                  <Save className=\"h-4 w-4 mr-2\" />\n                  {selectedCategoryForEdit ? \"更新\" : \"创建\"}\n                </>\n              )}\n            </Button>\n          </div>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAOA;AACA;AACA;AACA;AAOA;AAKA;AACA;;;AAhCA;;;;;;;;;;;;;;;AAkCA,SAAS;AACT,MAAM,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,KAAK;IAC7C,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,mBAAmB;IAC3C,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IACzB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;AAChC;AAIA,OAAO;AACP,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAIM,SAAS;;IACd,MAAM,EACJ,UAAU,EACV,aAAa,EACb,OAAO,EACP,UAAU,EACX,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,EACJ,oBAAoB,EACpB,uBAAuB,EACvB,mBAAmB,EACpB,GAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM;YACN,OAAO,aAAa,CAAC,EAAE;YACvB,MAAM;YACN,UAAU;YACV,WAAW;QACb;IACF;IAEA,iBAAiB;IACjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,yBAAyB;gBAC3B,MAAM;oBACJ,MAAM,wBAAwB,IAAI;oBAClC,OAAO,wBAAwB,KAAK;oBACpC,MAAM,wBAAwB,IAAI;oBAClC,UAAU,wBAAwB,QAAQ,IAAI;oBAC9C,WAAW,wBAAwB,SAAS;gBAC9C;YACF,OAAO;gBACL,MAAM;oBACJ,MAAM;oBACN,OAAO,aAAa,CAAC,EAAE;oBACvB,MAAM;oBACN,UAAU;oBACV,WAAW,WAAW,MAAM;gBAC9B;YACF;QACF;mCAAG;QAAC;QAAyB;QAAO,WAAW,MAAM;KAAC;IAEtD,OAAO;IACP,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa;YAEb,MAAM,eAAe;gBACnB,GAAG,IAAI;gBACP,UAAU,KAAK,QAAQ,KAAK,UAAU,CAAC,KAAK,QAAQ,GAAG,OAAO,KAAK,QAAQ;YAC7E;YAEA,IAAI,yBAAyB;gBAC3B,SAAS;gBACT,MAAM,kBAAkB,MAAM,oHAAA,CAAA,gBAAa,CAAC,MAAM,CAChD,wBAAwB,EAAE,EAC1B;gBAGF,SAAS;gBACT,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAA,MACvC,IAAI,EAAE,KAAK,wBAAwB,EAAE,GAAG,kBAAkB;gBAE5D,cAAc;gBAEd,iBAAiB;gBACjB,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,SACjC,OAAO,UAAU,KAAK,wBAAwB,EAAE,GAC5C;wBAAE,GAAG,MAAM;wBAAE,UAAU;oBAAgB,IACvC;gBAEN,WAAW;gBAEX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,QAAQ;gBACR,MAAM,cAAc,MAAM,oHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAE/C,UAAU;gBACV,cAAc;uBAAI;oBAAY;iBAAY;gBAE1C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBAAqB;IACrB,MAAM,+BAA+B;QACnC,IAAI,CAAC,yBAAyB,OAAO;QAErC,OAAO;QACP,OAAO,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,wBAAwB,EAAE;IACvE;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAsB,cAAc;kBAChD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCACT,0BAA0B,SAAS;;;;;;sCAEtC,6LAAC,qIAAA,CAAA,oBAAiB;sCACf,0BAA0B,kBAAkB;;;;;;;;;;;;8BAIjD,6LAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,aAAY;oCACX,GAAG,SAAS,OAAO;;;;;;gCAErB,OAAO,IAAI,kBACV,6LAAC;oCAAE,WAAU;8CAA4B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;sCAKhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC;4CAEC,MAAK;4CACL,WAAW,AAAC,gDAIX,OAHC,MAAM,aAAa,QACf,gCACA;4CAEN,OAAO;gDAAE,iBAAiB;4CAAM;4CAChC,SAAS,IAAM,SAAS,SAAS;2CAR5B;;;;;;;;;;8CAYX,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,OAAO,MAAM;oCACb,UAAU,CAAC,IAAM,SAAS,SAAS,EAAE,MAAM,CAAC,KAAK;oCACjD,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,MAAM;oCACb,eAAe,CAAC,QAAU,SAAS,QAAQ;;sDAE3C,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,qIAAA,CAAA,gBAAa;sDACX,uHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,qBACjB,6LAAC,qIAAA,CAAA,aAAU;oDAAkB,OAAO,KAAK,KAAK;8DAC5C,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAM,KAAK,IAAI;;;;;;0EAChB,6LAAC;0EAAM,KAAK,KAAK;;;;;;;;;;;;mDAHJ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAYnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,MAAM,eAAe;oCAC5B,eAAe,CAAC,QAAU,SAAS,YAAY,UAAU,SAAS,KAAK;;sDAEvE,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;gDACxB,+BAA+B,GAAG,CAAC,CAAC,yBACnC,6LAAC,qIAAA,CAAA,aAAU;wDAAmB,OAAO,SAAS,EAAE;kEAC9C,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,SAAS,KAAK;oEAAC;;;;;;gEAE1C,SAAS,IAAI;;;;;;;uDAND,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAepC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCAAI,WAAU;;wCACZ,CAAC;4CACA,MAAM,gBAAgB,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;4CAC7C,qBAAO,6LAAC;gDACN,WAAU;gDACV,OAAO;oDAAE,OAAO,MAAM;gDAAS;;;;;;wCAEnC,CAAC;sDACD,6LAAC;sDAAM,MAAM,WAAW;;;;;;;;;;;;;;;;;;sCAK5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,YACC,yBAEA;;0DACE,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,0BAA0B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD;GAhQgB;;QAMV,sHAAA,CAAA,iBAAc;QAMd,sHAAA,CAAA,mBAAgB;QAWhB,iKAAA,CAAA,UAAO;;;KAvBG", "debugId": null}}, {"offset": {"line": 7411, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/tag-dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { usePromptStore, useTagStore } from \"@/lib/store\";\nimport { tagsApi } from \"@/lib/api\";\n\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n  DialogDescription,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { \n  Save, \n  Hash\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\n\n// 表单验证模式\nconst tagSchema = z.object({\n  name: z.string().min(1, \"标签名称不能为空\").max(50, \"标签名称过长\"),\n  color: z.string().regex(/^#[0-9A-F]{6}$/i, \"请选择有效的颜色\"),\n});\n\ntype TagFormData = z.infer<typeof tagSchema>;\n\n// 预设颜色\nconst PRESET_COLORS = [\n  \"#EF4444\", // 红色\n  \"#F59E0B\", // 黄色\n  \"#10B981\", // 绿色\n  \"#3B82F6\", // 蓝色\n  \"#8B5CF6\", // 紫色\n  \"#F97316\", // 橙色\n  \"#06B6D4\", // 青色\n  \"#84CC16\", // 青绿色\n  \"#EC4899\", // 粉色\n  \"#6B7280\", // 灰色\n];\n\nexport function TagDialog() {\n  const {\n    tags,\n    setTags,\n    prompts,\n    setPrompts,\n  } = usePromptStore();\n\n  const {\n    isTagDialogOpen,\n    selectedTagForEdit,\n    closeTagDialog,\n  } = useTagStore();\n\n  const [isLoading, setIsLoading] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    watch,\n    reset,\n    formState: { errors },\n  } = useForm<TagFormData>({\n    resolver: zodResolver(tagSchema),\n    defaultValues: {\n      name: \"\",\n      color: PRESET_COLORS[0],\n    },\n  });\n\n  // 当选中的标签改变时，更新表单\n  useEffect(() => {\n    if (selectedTagForEdit) {\n      reset({\n        name: selectedTagForEdit.name,\n        color: selectedTagForEdit.color,\n      });\n    } else {\n      reset({\n        name: \"\",\n        color: PRESET_COLORS[0],\n      });\n    }\n  }, [selectedTagForEdit, reset]);\n\n  // 提交表单\n  const onSubmit = async (data: TagFormData) => {\n    try {\n      setIsLoading(true);\n\n      if (selectedTagForEdit) {\n        // 更新现有标签\n        const updatedTag = await tagsApi.update(selectedTagForEdit.id, data);\n\n        // 更新本地状态\n        const updatedTags = tags.map(tag =>\n          tag.id === selectedTagForEdit.id ? updatedTag : tag\n        );\n        setTags(updatedTags);\n\n        // 同时更新相关提示词的标签信息\n        const updatedPrompts = prompts.map(prompt => ({\n          ...prompt,\n          tags: prompt.tags?.map(tag =>\n            tag.id === selectedTagForEdit.id ? updatedTag : tag\n          ) || []\n        }));\n        setPrompts(updatedPrompts);\n\n        toast.success(\"标签已更新\");\n      } else {\n        // 创建新标签\n        const newTag = await tagsApi.create(data);\n\n        // 添加到本地状态\n        setTags([...tags, newTag]);\n\n        toast.success(\"标签已创建\");\n      }\n\n      closeTagDialog();\n    } catch (error) {\n      console.error(\"保存标签失败:\", error);\n      toast.error(\"保存标签失败\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <Dialog open={isTagDialogOpen} onOpenChange={closeTagDialog}>\n      <DialogContent className=\"max-w-md bg-slate-50 border-4 border-slate-800 shadow-2xl ring-8 ring-primary/20\">\n        <DialogHeader>\n          <DialogTitle>\n            {selectedTagForEdit ? \"编辑标签\" : \"新建标签\"}\n          </DialogTitle>\n          <DialogDescription>\n            {selectedTagForEdit ? \"修改标签的名称和颜色\" : \"创建一个新的标签来标记你的提示词\"}\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          {/* 标签名称 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"name\">标签名称 *</Label>\n            <Input\n              id=\"name\"\n              placeholder=\"输入标签名称...\"\n              {...register(\"name\")}\n            />\n            {errors.name && (\n              <p className=\"text-sm text-destructive\">{errors.name.message}</p>\n            )}\n          </div>\n\n          {/* 颜色选择 */}\n          <div className=\"space-y-2\">\n            <Label>颜色</Label>\n            <div className=\"flex flex-wrap gap-2\">\n              {PRESET_COLORS.map((color) => (\n                <button\n                  key={color}\n                  type=\"button\"\n                  className={`w-8 h-8 rounded-full border-2 transition-all ${\n                    watch(\"color\") === color \n                      ? \"border-foreground scale-110\" \n                      : \"border-muted hover:scale-105\"\n                  }`}\n                  style={{ backgroundColor: color }}\n                  onClick={() => setValue(\"color\", color)}\n                />\n              ))}\n            </div>\n            <Input\n              type=\"color\"\n              value={watch(\"color\")}\n              onChange={(e) => setValue(\"color\", e.target.value)}\n              className=\"w-20 h-10\"\n            />\n          </div>\n\n          {/* 预览 */}\n          <div className=\"space-y-2\">\n            <Label>预览</Label>\n            <div className=\"flex items-center gap-2 p-3 border rounded-lg\">\n              <Hash \n                className=\"h-4 w-4\" \n                style={{ color: watch(\"color\") }}\n              />\n              <span>#{watch(\"name\") || \"标签名称\"}</span>\n            </div>\n          </div>\n\n          {/* 操作按钮 */}\n          <div className=\"flex justify-end gap-3 pt-4 border-t\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={closeTagDialog}\n              disabled={isLoading}\n            >\n              取消\n            </Button>\n            <Button type=\"submit\" disabled={isLoading}>\n              {isLoading ? (\n                \"保存中...\"\n              ) : (\n                <>\n                  <Save className=\"h-4 w-4 mr-2\" />\n                  {selectedTagForEdit ? \"更新\" : \"创建\"}\n                </>\n              )}\n            </Button>\n          </div>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAOA;AACA;AACA;AACA;AAAA;AAIA;;;AAvBA;;;;;;;;;;;;;AAyBA,SAAS;AACT,MAAM,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,IAAI;IAC5C,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,mBAAmB;AAC7C;AAIA,OAAO;AACP,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS;;IACd,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACX,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,EACJ,eAAe,EACf,kBAAkB,EAClB,cAAc,EACf,GAAG,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAe;QACvB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM;YACN,OAAO,aAAa,CAAC,EAAE;QACzB;IACF;IAEA,iBAAiB;IACjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,oBAAoB;gBACtB,MAAM;oBACJ,MAAM,mBAAmB,IAAI;oBAC7B,OAAO,mBAAmB,KAAK;gBACjC;YACF,OAAO;gBACL,MAAM;oBACJ,MAAM;oBACN,OAAO,aAAa,CAAC,EAAE;gBACzB;YACF;QACF;8BAAG;QAAC;QAAoB;KAAM;IAE9B,OAAO;IACP,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa;YAEb,IAAI,oBAAoB;gBACtB,SAAS;gBACT,MAAM,aAAa,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,EAAE;gBAE/D,SAAS;gBACT,MAAM,cAAc,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,mBAAmB,EAAE,GAAG,aAAa;gBAElD,QAAQ;gBAER,iBAAiB;gBACjB,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA;wBAE3B;2BAFsC;wBAC5C,GAAG,MAAM;wBACT,MAAM,EAAA,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,GAAG,CAAC,CAAA,MACrB,IAAI,EAAE,KAAK,mBAAmB,EAAE,GAAG,aAAa,SAC7C,EAAE;oBACT;;gBACA,WAAW;gBAEX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,QAAQ;gBACR,MAAM,SAAS,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;gBAEpC,UAAU;gBACV,QAAQ;uBAAI;oBAAM;iBAAO;gBAEzB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAiB,cAAc;kBAC3C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCACT,qBAAqB,SAAS;;;;;;sCAEjC,6LAAC,qIAAA,CAAA,oBAAiB;sCACf,qBAAqB,eAAe;;;;;;;;;;;;8BAIzC,6LAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,aAAY;oCACX,GAAG,SAAS,OAAO;;;;;;gCAErB,OAAO,IAAI,kBACV,6LAAC;oCAAE,WAAU;8CAA4B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;sCAKhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC;4CAEC,MAAK;4CACL,WAAW,AAAC,gDAIX,OAHC,MAAM,aAAa,QACf,gCACA;4CAEN,OAAO;gDAAE,iBAAiB;4CAAM;4CAChC,SAAS,IAAM,SAAS,SAAS;2CAR5B;;;;;;;;;;8CAYX,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,OAAO,MAAM;oCACb,UAAU,CAAC,IAAM,SAAS,SAAS,EAAE,MAAM,CAAC,KAAK;oCACjD,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CACH,WAAU;4CACV,OAAO;gDAAE,OAAO,MAAM;4CAAS;;;;;;sDAEjC,6LAAC;;gDAAK;gDAAE,MAAM,WAAW;;;;;;;;;;;;;;;;;;;sCAK7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,YACC,yBAEA;;0DACE,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,qBAAqB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C;GAnLgB;;QAMV,sHAAA,CAAA,iBAAc;QAMd,sHAAA,CAAA,cAAW;QAWX,iKAAA,CAAA,UAAO;;;KAvBG", "debugId": null}}, {"offset": {"line": 7763, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/components/batch-toolbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { usePromptStore } from \"@/lib/store\";\nimport { promptsApi } from \"@/lib/api\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  Trash2, \n  FolderOpen, \n  X, \n  CheckSquare,\n  Square\n} from \"lucide-react\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n} from \"@/components/ui/dialog\";\nimport { toast } from \"sonner\";\nimport { cn } from \"@/lib/utils\";\n\nexport function BatchToolbar() {\n  const {\n    selectedPromptIds,\n    isBatchMode,\n    setBatchMode,\n    clearSelection,\n    selectAllPrompts,\n    prompts,\n    categories,\n    setPrompts,\n    filteredPrompts\n  } = usePromptStore();\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [selectedCategoryId, setSelectedCategoryId] = useState<string>(\"\");\n  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);\n\n  const filteredPromptsData = filteredPrompts();\n  const allSelected = filteredPromptsData.length > 0 && \n    filteredPromptsData.every(p => selectedPromptIds.includes(p.id));\n\n  // 切换全选\n  const handleToggleSelectAll = () => {\n    if (allSelected) {\n      clearSelection();\n    } else {\n      selectAllPrompts();\n    }\n  };\n\n  // 批量删除\n  const handleBatchDelete = async () => {\n    if (selectedPromptIds.length === 0) return;\n\n    setIsLoading(true);\n    try {\n      await promptsApi.deleteMany(selectedPromptIds);\n      \n      // 更新本地状态\n      const updatedPrompts = prompts.filter(p => !selectedPromptIds.includes(p.id));\n      setPrompts(updatedPrompts);\n      \n      toast.success(`成功删除 ${selectedPromptIds.length} 个提示词`);\n      clearSelection();\n      setIsDeleteDialogOpen(false);\n    } catch (error) {\n      console.error(\"批量删除失败:\", error);\n      toast.error(\"批量删除失败\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 批量移动分类\n  const handleBatchMoveCategory = async () => {\n    if (selectedPromptIds.length === 0 || !selectedCategoryId) return;\n\n    setIsLoading(true);\n    try {\n      const categoryId = selectedCategoryId === 'none' ? null : selectedCategoryId;\n      await promptsApi.updateCategory(selectedPromptIds, categoryId);\n      \n      // 更新本地状态\n      const updatedPrompts = prompts.map(p =>\n        selectedPromptIds.includes(p.id)\n          ? { ...p, categoryId: categoryId }\n          : p\n      );\n      setPrompts(updatedPrompts);\n      \n      const categoryName = categoryId \n        ? categories.find(c => c.id === categoryId)?.name || \"未知分类\"\n        : \"无分类\";\n      \n      toast.success(`成功将 ${selectedPromptIds.length} 个提示词移动到 ${categoryName}`);\n      clearSelection();\n      setSelectedCategoryId(\"\");\n    } catch (error) {\n      console.error(\"批量移动分类失败:\", error);\n      toast.error(\"批量移动分类失败\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!isBatchMode) return null;\n\n  return (\n    <div className={cn(\n      \"fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50\",\n      \"bg-white dark:bg-gray-800 border border-border rounded-xl shadow-xl\",\n      \"px-4 py-3 flex items-center gap-3\",\n      \"animate-slide-up backdrop-blur-sm\"\n    )}>\n      {/* 选择状态 */}\n      <div className=\"flex items-center gap-2\">\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleToggleSelectAll}\n          className=\"h-8 w-8 p-0\"\n        >\n          {allSelected ? (\n            <CheckSquare className=\"h-4 w-4 text-primary\" />\n          ) : (\n            <Square className=\"h-4 w-4\" />\n          )}\n        </Button>\n        <Badge variant=\"secondary\" className=\"font-medium\">\n          已选择 {selectedPromptIds.length} 项\n        </Badge>\n      </div>\n\n      {/* 分隔线 */}\n      <div className=\"h-6 w-px bg-border\" />\n\n      {/* 批量操作按钮 */}\n      <div className=\"flex items-center gap-2\">\n        {/* 移动分类 */}\n        <div className=\"flex items-center gap-2\">\n          <Select value={selectedCategoryId} onValueChange={setSelectedCategoryId}>\n            <SelectTrigger className=\"w-32 h-8 text-xs\">\n              <SelectValue placeholder=\"选择分类\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"none\">无分类</SelectItem>\n              {categories.map((category) => (\n                <SelectItem key={category.id} value={category.id}>\n                  {category.name}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleBatchMoveCategory}\n            disabled={isLoading || !selectedCategoryId || selectedPromptIds.length === 0}\n            className=\"h-8 gap-1\"\n          >\n            <FolderOpen className=\"h-3 w-3\" />\n            移动\n          </Button>\n        </div>\n\n        {/* 批量删除 */}\n        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>\n          <DialogTrigger asChild>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              disabled={isLoading || selectedPromptIds.length === 0}\n              className=\"h-8 gap-1 text-destructive hover:text-destructive\"\n            >\n              <Trash2 className=\"h-3 w-3\" />\n              删除\n            </Button>\n          </DialogTrigger>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>确认批量删除</DialogTitle>\n              <DialogDescription>\n                您确定要删除选中的 {selectedPromptIds.length} 个提示词吗？此操作无法撤销。\n              </DialogDescription>\n            </DialogHeader>\n            <DialogFooter>\n              <Button variant=\"outline\" onClick={() => setIsDeleteDialogOpen(false)}>\n                取消\n              </Button>\n              <Button\n                onClick={handleBatchDelete}\n                className=\"bg-destructive text-destructive-foreground hover:bg-destructive/90\"\n              >\n                确认删除\n              </Button>\n            </DialogFooter>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {/* 分隔线 */}\n      <div className=\"h-6 w-px bg-border\" />\n\n      {/* 关闭批量模式 */}\n      <Button\n        variant=\"ghost\"\n        size=\"sm\"\n        onClick={() => setBatchMode(false)}\n        className=\"h-8 w-8 p-0\"\n      >\n        <X className=\"h-4 w-4\" />\n      </Button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAOA;AASA;AACA;;;AA/BA;;;;;;;;;;;AAiCO,SAAS;;IACd,MAAM,EACJ,iBAAiB,EACjB,WAAW,EACX,YAAY,EACZ,cAAc,EACd,gBAAgB,EAChB,OAAO,EACP,UAAU,EACV,UAAU,EACV,eAAe,EAChB,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,sBAAsB;IAC5B,MAAM,cAAc,oBAAoB,MAAM,GAAG,KAC/C,oBAAoB,KAAK,CAAC,CAAA,IAAK,kBAAkB,QAAQ,CAAC,EAAE,EAAE;IAEhE,OAAO;IACP,MAAM,wBAAwB;QAC5B,IAAI,aAAa;YACf;QACF,OAAO;YACL;QACF;IACF;IAEA,OAAO;IACP,MAAM,oBAAoB;QACxB,IAAI,kBAAkB,MAAM,KAAK,GAAG;QAEpC,aAAa;QACb,IAAI;YACF,MAAM,oHAAA,CAAA,aAAU,CAAC,UAAU,CAAC;YAE5B,SAAS;YACT,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,CAAC,kBAAkB,QAAQ,CAAC,EAAE,EAAE;YAC3E,WAAW;YAEX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,QAAgC,OAAzB,kBAAkB,MAAM,EAAC;YAC/C;YACA,sBAAsB;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,SAAS;IACT,MAAM,0BAA0B;QAC9B,IAAI,kBAAkB,MAAM,KAAK,KAAK,CAAC,oBAAoB;QAE3D,aAAa;QACb,IAAI;gBAaE;YAZJ,MAAM,aAAa,uBAAuB,SAAS,OAAO;YAC1D,MAAM,oHAAA,CAAA,aAAU,CAAC,cAAc,CAAC,mBAAmB;YAEnD,SAAS;YACT,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,IACjC,kBAAkB,QAAQ,CAAC,EAAE,EAAE,IAC3B;oBAAE,GAAG,CAAC;oBAAE,YAAY;gBAAW,IAC/B;YAEN,WAAW;YAEX,MAAM,eAAe,aACjB,EAAA,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,yBAA9B,uCAAA,iBAA2C,IAAI,KAAI,SACnD;YAEJ,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,OAA0C,OAApC,kBAAkB,MAAM,EAAC,aAAwB,OAAb;YACzD;YACA,sBAAsB;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,aAAa,OAAO;IAEzB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,2DACA,uEACA,qCACA;;0BAGA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCAET,4BACC,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;iDAEvB,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAGtB,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAc;4BAC5C,kBAAkB,MAAM;4BAAC;;;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAoB,eAAe;;kDAChD,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0DACZ,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAO;;;;;;4CACxB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,aAAU;oDAAmB,OAAO,SAAS,EAAE;8DAC7C,SAAS,IAAI;mDADC,SAAS,EAAE;;;;;;;;;;;;;;;;;0CAMlC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,aAAa,CAAC,sBAAsB,kBAAkB,MAAM,KAAK;gCAC3E,WAAU;;kDAEV,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAMtC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAoB,cAAc;;0CAC9C,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,UAAU,aAAa,kBAAkB,MAAM,KAAK;oCACpD,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIlC,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,eAAY;;0DACX,6LAAC,qIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,6LAAC,qIAAA,CAAA,oBAAiB;;oDAAC;oDACN,kBAAkB,MAAM;oDAAC;;;;;;;;;;;;;kDAGxC,6LAAC,qIAAA,CAAA,eAAY;;0DACX,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,sBAAsB;0DAAQ;;;;;;0DAGvE,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa;gBAC5B,WAAU;0BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;GAlMgB;;QAWV,sHAAA,CAAA,iBAAc;;;KAXJ", "debugId": null}}, {"offset": {"line": 8142, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { usePromptStore } from \"@/lib/store\";\nimport { promptsApi, categoriesApi, tagsApi } from \"@/lib/api\";\nimport { AppSidebar } from \"@/components/app-sidebar\";\nimport { PromptGrid } from \"@/components/prompt-grid\";\nimport { SearchBar } from \"@/components/search-bar\";\nimport { PromptDialog } from \"@/components/prompt-dialog\";\nimport { CategoryDialog } from \"@/components/category-dialog\";\nimport { TagDialog } from \"@/components/tag-dialog\";\nimport { BatchToolbar } from \"@/components/batch-toolbar\";\nimport { SidebarProvider } from \"@/components/ui/sidebar\";\nimport { Button } from \"@/components/ui/button\";\nimport { Plus, CheckSquare } from \"lucide-react\";\nimport { toast } from \"sonner\";\n\nexport default function Home() {\n  const {\n    setPrompts,\n    setCategories,\n    setTags,\n    setIsLoading,\n    openPromptDialog,\n    isLoading,\n    // 🚀 批量操作相关状态\n    isBatchMode,\n    setBatchMode,\n    selectedPromptIds\n  } = usePromptStore();\n\n  // 加载初始数据 - 使用并行加载优化性能\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setIsLoading(true);\n\n        console.log(\"开始并行加载数据...\");\n\n        // 🚀 并行加载所有数据，大幅提升加载速度\n        // 从串行加载改为并行加载，预计提升 60-70% 性能\n        const [categoriesData, tagsData, promptsData] = await Promise.all([\n          categoriesApi.getAll(),\n          tagsApi.getAll(),\n          promptsApi.getAll()\n        ]);\n\n        console.log(\"所有数据加载完成，开始更新状态\");\n\n        // 批量更新状态，减少重新渲染次数\n        setCategories(categoriesData || []);\n        setTags(tagsData || []);\n        setPrompts(promptsData || []);\n\n        console.log(\"数据加载和状态更新完成\");\n      } catch (error) {\n        console.error(\"加载数据失败:\", error);\n        toast.error(`加载数据失败: ${error.message}`);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, [setPrompts, setCategories, setTags, setIsLoading]);\n\n  return (\n    <SidebarProvider>\n      <div className=\"flex h-screen w-full relative overflow-hidden\">\n\n\n        {/* 侧边栏 */}\n        <AppSidebar />\n\n        {/* 主内容区 */}\n        <main className=\"flex-1 flex flex-col overflow-hidden relative z-10\">\n          {/* 顶部工具栏 */}\n          <header className=\"glass-effect border-b border-border/30 backdrop-blur-xl\">\n            <div className=\"flex h-16 items-center justify-between px-4 md:px-6\">\n              <div className=\"flex items-center gap-4 flex-1\">\n                <SearchBar />\n              </div>\n\n              <div className=\"flex items-center gap-2\">\n                {/* 🚀 批量模式切换按钮 */}\n                <Button\n                  variant={isBatchMode ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setBatchMode(!isBatchMode)}\n                  className=\"gap-2 shrink-0 transition-all duration-200\"\n                >\n                  <CheckSquare className=\"h-4 w-4\" />\n                  <span className=\"hidden sm:inline\">\n                    {isBatchMode ? `批量 (${selectedPromptIds.length})` : \"批量\"}\n                  </span>\n                </Button>\n\n                <Button\n                  onClick={() => {\n                    console.log(\"新建提示词按钮被点击\");\n                    openPromptDialog();\n                  }}\n                  className=\"bg-primary text-primary-foreground hover:bg-primary/90 gap-2 shrink-0 transition-all duration-200\"\n                  size=\"sm\"\n                >\n                  <Plus className=\"h-4 w-4\" />\n                  <span className=\"hidden sm:inline\">新建提示词</span>\n                  <span className=\"sm:hidden\">新建</span>\n                </Button>\n              </div>\n            </div>\n          </header>\n\n          {/* 内容区域 */}\n          <div className=\"flex-1 overflow-auto p-4 md:p-6\">\n            {isLoading ? (\n              <div className=\"flex items-center justify-center h-64\">\n                <div className=\"flex flex-col items-center gap-4 animate-fade-in\">\n                  <div className=\"w-8 h-8 border-2 border-primary/30 border-t-primary rounded-full animate-spin\" />\n                  <div className=\"text-muted-foreground font-medium\">加载中...</div>\n                </div>\n              </div>\n            ) : (\n              <div className=\"animate-fade-in\">\n                <PromptGrid />\n              </div>\n            )}\n          </div>\n        </main>\n\n        {/* 🚀 批量操作工具栏 */}\n        <BatchToolbar />\n\n        {/* 提示词对话框 */}\n        <PromptDialog />\n\n        {/* 分类管理对话框 */}\n        <CategoryDialog />\n\n        {/* 标签管理对话框 */}\n        <TagDialog />\n      </div>\n    </SidebarProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAfA;;;;;;;;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,EACJ,UAAU,EACV,aAAa,EACb,OAAO,EACP,YAAY,EACZ,gBAAgB,EAChB,SAAS,EACT,cAAc;IACd,WAAW,EACX,YAAY,EACZ,iBAAiB,EAClB,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;IAEjB,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;2CAAW;oBACf,IAAI;wBACF,aAAa;wBAEb,QAAQ,GAAG,CAAC;wBAEZ,uBAAuB;wBACvB,6BAA6B;wBAC7B,MAAM,CAAC,gBAAgB,UAAU,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;4BAChE,oHAAA,CAAA,gBAAa,CAAC,MAAM;4BACpB,oHAAA,CAAA,UAAO,CAAC,MAAM;4BACd,oHAAA,CAAA,aAAU,CAAC,MAAM;yBAClB;wBAED,QAAQ,GAAG,CAAC;wBAEZ,kBAAkB;wBAClB,cAAc,kBAAkB,EAAE;wBAClC,QAAQ,YAAY,EAAE;wBACtB,WAAW,eAAe,EAAE;wBAE5B,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,AAAC,WAAwB,OAAd,MAAM,OAAO;oBACtC,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;yBAAG;QAAC;QAAY;QAAe;QAAS;KAAa;IAErD,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAIb,6LAAC,uIAAA,CAAA,aAAU;;;;;8BAGX,6LAAC;oBAAK,WAAU;;sCAEd,6LAAC;4BAAO,WAAU;sCAChB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,sIAAA,CAAA,YAAS;;;;;;;;;;kDAGZ,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,cAAc,YAAY;gDACnC,MAAK;gDACL,SAAS,IAAM,aAAa,CAAC;gDAC7B,WAAU;;kEAEV,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC;wDAAK,WAAU;kEACb,cAAc,AAAC,OAA+B,OAAzB,kBAAkB,MAAM,EAAC,OAAK;;;;;;;;;;;;0DAIxD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;oDACP,QAAQ,GAAG,CAAC;oDACZ;gDACF;gDACA,WAAU;gDACV,MAAK;;kEAEL,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,6LAAC;wDAAK,WAAU;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOpC,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;sDAAoC;;;;;;;;;;;;;;;;qDAIvD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;8BAOnB,6LAAC,yIAAA,CAAA,eAAY;;;;;8BAGb,6LAAC,yIAAA,CAAA,eAAY;;;;;8BAGb,6LAAC,2IAAA,CAAA,iBAAc;;;;;8BAGf,6LAAC,sIAAA,CAAA,YAAS;;;;;;;;;;;;;;;;AAIlB;GA/HwB;;QAYlB,sHAAA,CAAA,iBAAc;;;KAZI", "debugId": null}}]}
import { NextRequest, NextResponse } from 'next/server'
import { promptsApi } from '@/lib/database'

// POST /api/prompts/[id]/usage - 增加使用次数
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await promptsApi.incrementUsage(params.id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('增加使用次数失败:', error)
    return NextResponse.json(
      { error: '增加使用次数失败' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { promptsApi } from '@/lib/database'
import { invalidateCache, CACHE_CONFIG } from '@/lib/cache'

// DELETE /api/prompts/batch - 批量删除提示词
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()
    const { ids } = body
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: '请提供要删除的提示词 ID 列表' },
        { status: 400 }
      )
    }

    // 批量删除
    await promptsApi.deleteMany(ids)
    
    // 清除相关缓存
    invalidateCache(CACHE_CONFIG.INVALIDATION_PATTERNS.PROMPTS)
    
    return NextResponse.json({
      success: true,
      message: `成功删除 ${ids.length} 个提示词`,
      deletedCount: ids.length
    })
  } catch (error) {
    console.error('批量删除提示词失败:', error)
    return NextResponse.json(
      { error: '批量删除失败' },
      { status: 500 }
    )
  }
}

// PUT /api/prompts/batch - 批量更新提示词
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { ids, action, data } = body
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: '请提供要更新的提示词 ID 列表' },
        { status: 400 }
      )
    }

    if (!action) {
      return NextResponse.json(
        { error: '请指定操作类型' },
        { status: 400 }
      )
    }

    let message = ''
    
    switch (action) {
      case 'move_category':
        const { categoryId } = data || {}
        await promptsApi.updateCategory(ids, categoryId)
        message = `成功移动 ${ids.length} 个提示词到新分类`
        break
        
      default:
        return NextResponse.json(
          { error: '不支持的操作类型' },
          { status: 400 }
        )
    }
    
    // 清除相关缓存
    invalidateCache(CACHE_CONFIG.INVALIDATION_PATTERNS.PROMPTS)
    
    return NextResponse.json({
      success: true,
      message,
      updatedCount: ids.length
    })
  } catch (error) {
    console.error('批量更新提示词失败:', error)
    return NextResponse.json(
      { error: '批量更新失败' },
      { status: 500 }
    )
  }
}

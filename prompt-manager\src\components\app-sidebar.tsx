"use client";

import { usePromptStore, useCategoryStore, useTagStore } from "@/lib/store";
import { categoriesApi, tagsApi, type Tag } from "@/lib/api";
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Folder,
  FolderOpen,
  Plus,
  Hash,
  Sparkles,
  MoreHorizontal,
  Edit,
  Trash2
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { useState } from "react";
import { getIconComponent } from "@/lib/icons";

export function AppSidebar() {
  const {
    categories,
    tags,
    prompts,
    selectedCategory,
    selectedTags,
    setSelectedCategory,
    setSelectedTags,
    setCategories,
    setTags,
  } = usePromptStore();

  const { openCategoryDialog } = useCategoryStore();
  const { openTagDialog } = useTagStore();
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  // 计算每个分类的提示词数量
  const getCategoryCount = (categoryId: string) => {
    return prompts.filter(prompt => prompt.categoryId === categoryId).length;
  };

  // 计算每个标签的提示词数量
  const getTagCount = (tagId: string) => {
    return prompts.filter(prompt => 
      prompt.tags?.some(tag => tag.id === tagId)
    ).length;
  };

  // 处理分类选择
  const handleCategorySelect = (categoryId: string | null) => {
    setSelectedCategory(categoryId);
  };

  // 处理标签选择
  const handleTagSelect = (tagId: string) => {
    const newSelectedTags = selectedTags.includes(tagId)
      ? selectedTags.filter(id => id !== tagId)
      : [...selectedTags, tagId];
    setSelectedTags(newSelectedTags);
  };

  // 编辑分类
  const handleEditCategory = (category: any) => {
    openCategoryDialog(category);
  };

  // 删除分类
  const handleDeleteCategory = async (categoryId: string) => {
    if (!confirm("确定要删除这个分类吗？删除后该分类下的提示词将变为无分类。")) {
      return;
    }

    try {
      setIsDeleting(categoryId);
      await categoriesApi.delete(categoryId);

      // 更新本地状态
      const updatedCategories = categories.filter(cat => cat.id !== categoryId);
      setCategories(updatedCategories);

      // 如果当前选中的分类被删除，清除选择
      if (selectedCategory === categoryId) {
        setSelectedCategory(null);
      }

      toast.success("分类已删除");
    } catch (error) {
      console.error("删除分类失败:", error);
      toast.error("删除分类失败");
    } finally {
      setIsDeleting(null);
    }
  };

  // 编辑标签
  const handleEditTag = (tag: Tag) => {
    openTagDialog(tag);
  };

  // 删除标签
  const handleDeleteTag = async (tagId: string) => {
    if (!confirm("确定要删除这个标签吗？删除后该标签下的提示词将失去此标签。")) {
      return;
    }

    try {
      setIsDeleting(tagId);
      await tagsApi.delete(tagId);

      // 更新本地状态
      const updatedTags = tags.filter(tag => tag.id !== tagId);
      setTags(updatedTags);

      // 如果当前选中的标签被删除，清除选择
      if (selectedTags.includes(tagId)) {
        setSelectedTags(selectedTags.filter(id => id !== tagId));
      }

      toast.success("标签已删除");
    } catch (error) {
      console.error("删除标签失败:", error);
      toast.error("删除标签失败");
    } finally {
      setIsDeleting(null);
    }
  };

  return (
    <Sidebar className="bg-sidebar border-r-2 border-sidebar-border">
      <SidebarHeader className="border-b-2 border-sidebar-border px-6 py-4">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-xl bg-primary shadow-md">
            <Sparkles className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent">
              提示词管理
            </h1>
            <p className="text-xs text-muted-foreground/70">智能提示词工具</p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-4 py-4 space-y-6">
        {/* 全部提示词 */}
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              onClick={() => handleCategorySelect(null)}
              className={cn(
                "w-full justify-between rounded-xl transition-all duration-300",
                "hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10",
                "hover:shadow-md hover:scale-[1.02]",
                selectedCategory === null && "bg-gradient-to-r from-blue-500/15 to-purple-500/15 shadow-md border border-blue-500/20"
              )}
            >
              <div className="flex items-center gap-3">
                <div className={cn(
                  "p-1.5 rounded-lg transition-all duration-300",
                  selectedCategory === null
                    ? "bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg"
                    : "bg-muted/50 text-muted-foreground"
                )}>
                  <Folder className="h-4 w-4" />
                </div>
                <span className="font-medium">全部提示词</span>
              </div>
              <Badge
                variant="secondary"
                className={cn(
                  "ml-auto font-bold transition-all duration-300",
                  selectedCategory === null
                    ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md"
                    : "bg-muted/70"
                )}
              >
                {prompts.length}
              </Badge>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>

        {/* 分类 */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-bold text-muted-foreground/80 uppercase tracking-wider">分类</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => openCategoryDialog()}
              className={cn(
                "h-7 w-7 p-0 rounded-lg transition-all duration-300",
                "hover:bg-gradient-to-r hover:from-green-500/20 hover:to-emerald-500/20",
                "hover:scale-110 hover:shadow-md"
              )}
            >
              <Plus className="h-3.5 w-3.5" />
            </Button>
          </div>

          <SidebarMenu className="space-y-2">
            {categories.map((category) => (
              <SidebarMenuItem key={category.id}>
                <div className="flex items-center group">
                  <SidebarMenuButton
                    onClick={() => handleCategorySelect(category.id)}
                    className={cn(
                      "flex-1 justify-between rounded-xl transition-all duration-300",
                      "hover:bg-gradient-to-r hover:shadow-md hover:scale-[1.02]",
                      selectedCategory === category.id
                        ? "shadow-md border"
                        : "hover:from-gray-500/10 hover:to-gray-600/10"
                    )}
                    style={{
                      ...(selectedCategory === category.id && {
                        background: `linear-gradient(135deg, ${category.color}15, ${category.color}25)`,
                        borderColor: `${category.color}30`,
                        boxShadow: `0 4px 12px ${category.color}20`
                      })
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={cn(
                          "p-1.5 rounded-lg transition-all duration-300",
                          selectedCategory === category.id
                            ? "shadow-lg text-white"
                            : "bg-muted/50"
                        )}
                        style={{
                          ...(selectedCategory === category.id && {
                            background: `linear-gradient(135deg, ${category.color}, ${category.color}dd)`
                          })
                        }}
                      >
                        {(() => {
                          const IconComponent = getIconComponent(category.icon, selectedCategory === category.id);
                          return <IconComponent className="h-4 w-4" style={{ color: selectedCategory === category.id ? 'white' : category.color }} />;
                        })()}
                      </div>
                      <span className="truncate font-medium">{category.name}</span>
                    </div>
                    <Badge
                      variant="secondary"
                      className={cn(
                        "ml-auto font-bold transition-all duration-300",
                        selectedCategory === category.id && "text-white shadow-md"
                      )}
                      style={{
                        ...(selectedCategory === category.id && {
                          background: category.color,
                          color: 'white'
                        })
                      }}
                    >
                      {getCategoryCount(category.id)}
                    </Badge>
                  </SidebarMenuButton>

                  {/* 分类操作菜单 */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "h-8 w-8 p-0 rounded-lg transition-all duration-300",
                          "opacity-60 hover:opacity-100",
                          "hover:bg-gradient-to-r hover:from-gray-500/20 hover:to-gray-600/20",
                          "hover:scale-110"
                        )}
                        disabled={isDeleting === category.id}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="glass-effect border-border/50">
                      <DropdownMenuItem onClick={() => handleEditCategory(category)} className="hover:bg-blue-500/10">
                        <Edit className="h-4 w-4 mr-2" />
                        编辑分类
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteCategory(category.id)}
                        className="text-destructive focus:text-destructive hover:bg-red-500/10"
                        disabled={isDeleting === category.id}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        删除分类
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>

          {categories.length === 0 && (
            <div className="text-sm text-muted-foreground/60 text-center py-6 px-4 rounded-xl bg-gradient-to-br from-gray-50/50 to-gray-100/30 dark:from-gray-800/30 dark:to-gray-900/20 border border-dashed border-border/50">
              <Folder className="h-8 w-8 mx-auto mb-2 text-muted-foreground/40" />
              <p>暂无分类</p>
              <p className="text-xs mt-1">点击上方 + 号创建分类</p>
            </div>
          )}
        </div>

        {/* 标签 */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-bold text-muted-foreground/80 uppercase tracking-wider">标签</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => openTagDialog()}
              className={cn(
                "h-7 w-7 p-0 rounded-lg transition-all duration-300",
                "hover:bg-gradient-to-r hover:from-purple-500/20 hover:to-pink-500/20",
                "hover:scale-110 hover:shadow-md"
              )}
            >
              <Plus className="h-3.5 w-3.5" />
            </Button>
          </div>

          <SidebarMenu className="space-y-2">
            {tags.map((tag) => (
              <SidebarMenuItem key={tag.id}>
                <div className="flex items-center gap-1">
                  <SidebarMenuButton
                    onClick={() => handleTagSelect(tag.id)}
                    className={cn(
                      "flex-1 justify-between rounded-xl transition-all duration-300",
                      "hover:bg-gradient-to-r hover:shadow-md hover:scale-[1.02]",
                      selectedTags.includes(tag.id)
                        ? "shadow-md border"
                        : "hover:from-gray-500/10 hover:to-gray-600/10"
                    )}
                    style={{
                      ...(selectedTags.includes(tag.id) && {
                        background: `linear-gradient(135deg, ${tag.color}15, ${tag.color}25)`,
                        borderColor: `${tag.color}30`,
                        boxShadow: `0 4px 12px ${tag.color}20`
                      })
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={cn(
                          "p-1.5 rounded-lg transition-all duration-300",
                          selectedTags.includes(tag.id)
                            ? "shadow-lg text-white"
                            : "bg-muted/50"
                        )}
                        style={{
                          ...(selectedTags.includes(tag.id) && {
                            background: `linear-gradient(135deg, ${tag.color}, ${tag.color}dd)`
                          })
                        }}
                      >
                        <Hash
                          className="h-4 w-4"
                          style={{
                            color: selectedTags.includes(tag.id) ? 'white' : tag.color
                          }}
                        />
                      </div>
                      <span className="truncate font-medium">{tag.name}</span>
                    </div>
                    <Badge
                      variant="secondary"
                      className={cn(
                        "ml-auto font-bold transition-all duration-300",
                        selectedTags.includes(tag.id) && "text-white shadow-md"
                      )}
                      style={{
                        ...(selectedTags.includes(tag.id) && {
                          background: tag.color,
                          color: 'white'
                        })
                      }}
                    >
                      {getTagCount(tag.id)}
                    </Badge>
                  </SidebarMenuButton>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 opacity-60 hover:opacity-100 transition-opacity"
                        disabled={isDeleting === tag.id}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="glass-effect border-border/50">
                      <DropdownMenuItem onClick={() => handleEditTag(tag)} className="hover:bg-blue-500/10">
                        <Edit className="h-4 w-4 mr-2" />
                        编辑标签
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteTag(tag.id)}
                        className="text-destructive focus:text-destructive hover:bg-red-500/10"
                        disabled={isDeleting === tag.id}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        删除标签
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>

          {tags.length === 0 && (
            <div className="text-sm text-muted-foreground/60 text-center py-6 px-4 rounded-xl bg-gradient-to-br from-gray-50/50 to-gray-100/30 dark:from-gray-800/30 dark:to-gray-900/20 border border-dashed border-border/50">
              <Hash className="h-8 w-8 mx-auto mb-2 text-muted-foreground/40" />
              <p>暂无标签</p>
              <p className="text-xs mt-1">点击上方 + 号创建标签</p>
            </div>
          )}
        </div>

        {/* 统计信息 */}
        <div className="pt-4 border-t border-border/30">
          <div className="p-4 rounded-xl bg-gradient-to-br from-gray-50/80 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-900/30 border border-border/30">
            <h4 className="text-xs font-bold text-muted-foreground/80 uppercase tracking-wider mb-3">统计信息</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-600"></div>
                  <span className="text-xs font-medium">总提示词</span>
                </div>
                <span className="text-xs font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
                  {prompts.length}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-600"></div>
                  <span className="text-xs font-medium">分类数</span>
                </div>
                <span className="text-xs font-bold bg-gradient-to-r from-green-500 to-emerald-600 bg-clip-text text-transparent">
                  {categories.length}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-600"></div>
                  <span className="text-xs font-medium">标签数</span>
                </div>
                <span className="text-xs font-bold bg-gradient-to-r from-purple-500 to-pink-600 bg-clip-text text-transparent">
                  {tags.length}
                </span>
              </div>
            </div>
          </div>
        </div>
      </SidebarContent>

      <SidebarRail />
    </Sidebar>
  );
}

"use client";

import { useState, useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { usePromptStore } from "@/lib/store";
import { promptsApi, tagsApi } from "@/lib/api";
import type { Prompt } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Save,
  Plus,
  Eye,
  Edit,
  Copy
} from "lucide-react";
import { toast } from "sonner";
import ReactMarkdown from "react-markdown";
import remarkBreaks from 'remark-breaks';

// 表单验证模式
const promptSchema = z.object({
  title: z.string().min(1, "标题不能为空").max(255, "标题过长"),
  description: z.string().optional(),
  content: z.string().min(1, "内容不能为空"),
  categoryId: z.string().optional(),
});

type PromptFormData = z.infer<typeof promptSchema>;

interface PromptFormProps {
  prompt?: Prompt | null;
  onSuccess?: () => void;
}

export function PromptForm({ prompt, onSuccess }: PromptFormProps) {
  const {
    categories,
    tags,
    prompts,
    setPrompts,
    setTags,
  } = usePromptStore();

  const [isLoading, setIsLoading] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [newTagName, setNewTagName] = useState("");

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<PromptFormData>({
    resolver: zodResolver(promptSchema),
    defaultValues: {
      title: prompt?.title || "",
      description: prompt?.description || "",
      content: prompt?.content || "",
      categoryId: prompt?.categoryId || "none",
    },
  });

  // 监听内容变化，用于预览
  const contentValue = watch("content");

  // 复制代码到剪贴板
  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success("代码已复制到剪贴板");
    } catch (error) {
      console.error("复制失败:", error);
      toast.error("复制失败");
    }
  };

  // 缓存 Markdown 组件配置
  const markdownComponents = useMemo(() => ({
    code: ({ inline, children, ...props }: any) => {
      if (inline) {
        return (
          <code
            className="bg-muted px-1 py-0.5 rounded text-sm font-mono"
            {...props}
          >
            {children}
          </code>
        );
      }
      return (
        <div className="relative group mb-4">
          <Button
            size="sm"
            variant="secondary"
            className="absolute top-2 right-2 opacity-80 hover:opacity-100 transition-opacity z-10 bg-gray-700 hover:bg-gray-600 text-white border-gray-600"
            onClick={() => handleCopyCode(String(children))}
          >
            <Copy className="h-4 w-4 mr-1" />
            复制
          </Button>
          <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
            <code className="font-mono text-sm" {...props}>
              {children}
            </code>
          </pre>
        </div>
      );
    },
    p: ({ children }: any) => (
      <p className="mb-4 leading-relaxed">{children}</p>
    ),
    ul: ({ children }: any) => (
      <ul className="mb-4 pl-6 space-y-1">{children}</ul>
    ),
    ol: ({ children }: any) => (
      <ol className="mb-4 pl-6 space-y-1">{children}</ol>
    ),
  }), []);

  // 初始化选中的标签
  useEffect(() => {
    if (prompt?.tags) {
      setSelectedTags(prompt.tags.map(tag => tag.id));
    }
  }, [prompt]);

  // 处理标签选择
  const handleTagToggle = (tagId: string) => {
    setSelectedTags(prev => 
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  // 添加新标签
  const handleAddNewTag = async () => {
    if (!newTagName.trim()) return;

    try {
      // 调用 API 创建新标签
      const newTag = await tagsApi.create({
        name: newTagName.trim(),
        color: '#3B82F6' // 默认蓝色
      });

      // 更新本地标签列表
      setTags([...tags, newTag]);

      // 自动选中新创建的标签
      setSelectedTags(prev => [...prev, newTag.id]);

      // 清空输入框
      setNewTagName("");

      toast.success("标签已创建并添加");
    } catch (error) {
      console.error("创建标签失败:", error);
      toast.error("创建标签失败");
    }
  };

  // 提交表单
  const onSubmit = async (data: PromptFormData) => {
    try {
      setIsLoading(true);

      if (prompt) {
        // 更新现有提示词
        const updatedPrompt = await promptsApi.update(prompt.id, {
          ...data,
          categoryId: data.categoryId === "none" || !data.categoryId ? null : data.categoryId,
          tagIds: selectedTags,
        });

        // 更新本地状态
        const updatedPrompts = prompts.map(p =>
          p.id === prompt.id ? updatedPrompt : p
        );
        setPrompts(updatedPrompts);

        toast.success("提示词已更新");
      } else {
        // 创建新提示词
        const newPrompt = await promptsApi.create({
          ...data,
          categoryId: data.categoryId === "none" || !data.categoryId ? null : data.categoryId,
          tagIds: selectedTags,
        });

        // 添加到本地状态
        setPrompts([newPrompt, ...prompts]);

        toast.success("提示词已创建");
      }

      onSuccess?.();
    } catch (error) {
      console.error("保存失败:", error);
      toast.error("保存失败");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* 标题 */}
      <div className="space-y-2">
        <Label htmlFor="title">标题 *</Label>
        <Input
          id="title"
          placeholder="输入提示词标题..."
          {...register("title")}
        />
        {errors.title && (
          <p className="text-sm text-destructive">{errors.title.message}</p>
        )}
      </div>

      {/* 描述 */}
      <div className="space-y-2">
        <Label htmlFor="description">描述</Label>
        <Input
          id="description"
          placeholder="简短描述这个提示词的用途..."
          {...register("description")}
        />
        {errors.description && (
          <p className="text-sm text-destructive">{errors.description.message}</p>
        )}
      </div>

      {/* 分类 */}
      <div className="space-y-2">
        <Label>分类</Label>
        <Select
          value={watch("categoryId") || "none"}
          onValueChange={(value) => setValue("categoryId", value === "none" ? "" : value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="选择分类..." />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">无分类</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: category.color }}
                  />
                  {category.name}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 标签 */}
      <div className="space-y-2">
        <Label>标签</Label>
        <div className="flex flex-wrap gap-2 mb-3">
          {tags.map((tag) => (
            <Badge
              key={tag.id}
              variant={selectedTags.includes(tag.id) ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => handleTagToggle(tag.id)}
            >
              #{tag.name}
            </Badge>
          ))}
        </div>
        
        {/* 添加新标签 */}
        <div className="flex gap-2">
          <Input
            placeholder="添加新标签..."
            value={newTagName}
            onChange={(e) => setNewTagName(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleAddNewTag();
              }
            }}
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleAddNewTag}
            disabled={!newTagName.trim()}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 内容 */}
      <div className="space-y-2">
        <Label htmlFor="content">内容 *</Label>
        <Tabs defaultValue="edit" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="edit" className="flex items-center gap-2">
              <Edit className="h-4 w-4" />
              编辑
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              预览
            </TabsTrigger>
          </TabsList>

          <TabsContent value="edit" className="mt-2">
            <Textarea
              id="content"
              placeholder="输入提示词内容，支持 Markdown 格式..."
              rows={12}
              {...register("content")}
              className="font-mono"
            />
          </TabsContent>

          <TabsContent value="preview" className="mt-2">
            <div className="min-h-[300px] p-4 border rounded-md bg-muted/50">
              {contentValue ? (
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <ReactMarkdown
                    components={markdownComponents}
                    remarkPlugins={[remarkBreaks]}
                  >
                    {contentValue}
                  </ReactMarkdown>
                </div>
              ) : (
                <div className="text-muted-foreground text-center py-12">
                  在编辑器中输入内容以查看预览
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        {errors.content && (
          <p className="text-sm text-destructive">{errors.content.message}</p>
        )}
        <p className="text-xs text-muted-foreground">
          支持 Markdown 格式，包括代码块、列表、链接等。可以在预览标签页查看渲染效果。
        </p>
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-end gap-3 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onSuccess}
          disabled={isLoading}
        >
          取消
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            "保存中..."
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {prompt ? "更新" : "创建"}
            </>
          )}
        </Button>
      </div>
    </form>
  );
}

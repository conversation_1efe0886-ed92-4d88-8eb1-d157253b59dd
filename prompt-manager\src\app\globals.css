@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* 专业色彩方案 - 企业级设计 */
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

:root {
  --radius: 0.75rem;

  /* 专业配色方案 - 浅色模式 */
  --background: #f1f5f9;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;

  /* 主色调 - 强化蓝色 */
  --primary: #1e40af;
  --primary-foreground: #ffffff;

  /* 次要色调 */
  --secondary: #e2e8f0;
  --secondary-foreground: #1e293b;

  /* 静音色调 */
  --muted: #f8fafc;
  --muted-foreground: #475569;

  /* 强调色 */
  --accent: #dbeafe;
  --accent-foreground: #0c4a6e;

  /* 危险色 */
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;

  /* 边框和输入 */
  --border: #cbd5e1;
  --input: #ffffff;
  --ring: #1e40af;

  /* 图表颜色 */
  --chart-1: #1d4ed8;
  --chart-2: #0891b2;
  --chart-3: #059669;
  --chart-4: #d97706;
  --chart-5: #dc2626;

  /* 侧边栏 */
  --sidebar: #ffffff;
  --sidebar-foreground: #1e293b;
  --sidebar-primary: #1e40af;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #e0f2fe;
  --sidebar-accent-foreground: #0c4a6e;
  --sidebar-border: #cbd5e1;
  --sidebar-ring: #1e40af;
}

.dark {
  /* 专业配色方案 - 深色模式 */
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;

  /* 主色调 - 品牌蓝色 */
  --primary: #3b82f6;
  --primary-foreground: #ffffff;

  /* 次要色调 */
  --secondary: #334155;
  --secondary-foreground: #cbd5e1;

  /* 静音色调 */
  --muted: #334155;
  --muted-foreground: #94a3b8;

  /* 强调色 */
  --accent: #1e40af;
  --accent-foreground: #dbeafe;

  /* 危险色 */
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;

  /* 边框和输入 */
  --border: #475569;
  --input: #1e293b;
  --ring: #3b82f6;

  /* 图表颜色 - 深色模式 */
  --chart-1: #3b82f6;
  --chart-2: #06b6d4;
  --chart-3: #10b981;
  --chart-4: #f59e0b;
  --chart-5: #ef4444;

  /* 侧边栏 - 深色模式 */
  --sidebar: #1e293b;
  --sidebar-foreground: #cbd5e1;
  --sidebar-primary: #3b82f6;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #334155;
  --sidebar-accent-foreground: #cbd5e1;
  --sidebar-border: #475569;
  --sidebar-ring: #3b82f6;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply text-foreground;
    background: var(--background);
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.5);
  }

  .dark ::-webkit-scrollbar-thumb {
    background: rgba(71, 85, 105, 0.5);
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: rgba(71, 85, 105, 0.7);
  }
}

@layer components {
  /* 专业卡片样式 */
  .professional-card {
    @apply bg-card border-2 border-border rounded-lg shadow-md;
    transition: all 0.2s ease-in-out;
  }

  .professional-card:hover {
    @apply shadow-lg border-primary/20;
  }

  /* 专业按钮样式 */
  .professional-button {
    @apply bg-primary text-primary-foreground border-0 shadow-sm transition-colors duration-200;
  }

  .professional-button:hover {
    @apply bg-primary/90;
  }

  /* 专业输入框样式 */
  .professional-input {
    @apply bg-input border border-border rounded-md;
    transition: border-color 0.2s ease-in-out;
  }

  .professional-input:focus {
    @apply ring-2 ring-ring border-ring;
  }

  /* 基础动画类 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.2s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.15s ease-out;
  }

  /* 加载动画 */
  .loading-spinner {
    animation: spin 1s linear infinite;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 专业网格布局 */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1rem;
  align-items: start;
}

/* 固定高度卡片布局 */
.responsive-grid.fixed-height {
  grid-auto-rows: 1fr;
}

.responsive-grid .card-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

@media (max-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 0.75rem;
  }
}

@media (max-width: 640px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

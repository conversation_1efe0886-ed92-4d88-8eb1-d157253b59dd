"use client";

import { useState } from "react";
import { usePromptStore } from "@/lib/store";
import { promptsApi } from "@/lib/api";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Trash2, 
  FolderOpen, 
  X, 
  CheckSquare,
  Square
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

export function BatchToolbar() {
  const {
    selectedPromptIds,
    isBatchMode,
    setBatchMode,
    clearSelection,
    selectAllPrompts,
    prompts,
    categories,
    setPrompts,
    filteredPrompts
  } = usePromptStore();

  const [isLoading, setIsLoading] = useState(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const filteredPromptsData = filteredPrompts();
  const allSelected = filteredPromptsData.length > 0 && 
    filteredPromptsData.every(p => selectedPromptIds.includes(p.id));

  // 切换全选
  const handleToggleSelectAll = () => {
    if (allSelected) {
      clearSelection();
    } else {
      selectAllPrompts();
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedPromptIds.length === 0) return;

    setIsLoading(true);
    try {
      await promptsApi.deleteMany(selectedPromptIds);
      
      // 更新本地状态
      const updatedPrompts = prompts.filter(p => !selectedPromptIds.includes(p.id));
      setPrompts(updatedPrompts);
      
      toast.success(`成功删除 ${selectedPromptIds.length} 个提示词`);
      clearSelection();
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("批量删除失败:", error);
      toast.error("批量删除失败");
    } finally {
      setIsLoading(false);
    }
  };

  // 批量移动分类
  const handleBatchMoveCategory = async () => {
    if (selectedPromptIds.length === 0 || !selectedCategoryId) return;

    setIsLoading(true);
    try {
      const categoryId = selectedCategoryId === 'none' ? null : selectedCategoryId;
      await promptsApi.updateCategory(selectedPromptIds, categoryId);
      
      // 更新本地状态
      const updatedPrompts = prompts.map(p =>
        selectedPromptIds.includes(p.id)
          ? { ...p, categoryId: categoryId }
          : p
      );
      setPrompts(updatedPrompts);
      
      const categoryName = categoryId 
        ? categories.find(c => c.id === categoryId)?.name || "未知分类"
        : "无分类";
      
      toast.success(`成功将 ${selectedPromptIds.length} 个提示词移动到 ${categoryName}`);
      clearSelection();
      setSelectedCategoryId("");
    } catch (error) {
      console.error("批量移动分类失败:", error);
      toast.error("批量移动分类失败");
    } finally {
      setIsLoading(false);
    }
  };

  if (!isBatchMode) return null;

  return (
    <div className={cn(
      "fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50",
      "bg-white dark:bg-gray-800 border border-border rounded-xl shadow-xl",
      "px-4 py-3 flex items-center gap-3",
      "animate-slide-up backdrop-blur-sm"
    )}>
      {/* 选择状态 */}
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleToggleSelectAll}
          className="h-8 w-8 p-0"
        >
          {allSelected ? (
            <CheckSquare className="h-4 w-4 text-primary" />
          ) : (
            <Square className="h-4 w-4" />
          )}
        </Button>
        <Badge variant="secondary" className="font-medium">
          已选择 {selectedPromptIds.length} 项
        </Badge>
      </div>

      {/* 分隔线 */}
      <div className="h-6 w-px bg-border" />

      {/* 批量操作按钮 */}
      <div className="flex items-center gap-2">
        {/* 移动分类 */}
        <div className="flex items-center gap-2">
          <Select value={selectedCategoryId} onValueChange={setSelectedCategoryId}>
            <SelectTrigger className="w-32 h-8 text-xs">
              <SelectValue placeholder="选择分类" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">无分类</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={handleBatchMoveCategory}
            disabled={isLoading || !selectedCategoryId || selectedPromptIds.length === 0}
            className="h-8 gap-1"
          >
            <FolderOpen className="h-3 w-3" />
            移动
          </Button>
        </div>

        {/* 批量删除 */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={isLoading || selectedPromptIds.length === 0}
              className="h-8 gap-1 text-destructive hover:text-destructive"
            >
              <Trash2 className="h-3 w-3" />
              删除
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>确认批量删除</DialogTitle>
              <DialogDescription>
                您确定要删除选中的 {selectedPromptIds.length} 个提示词吗？此操作无法撤销。
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                取消
              </Button>
              <Button
                onClick={handleBatchDelete}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                确认删除
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* 分隔线 */}
      <div className="h-6 w-px bg-border" />

      {/* 关闭批量模式 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setBatchMode(false)}
        className="h-8 w-8 p-0"
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  );
}

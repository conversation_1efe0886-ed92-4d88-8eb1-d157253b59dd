{"name": "prompt-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/react-fontawesome": "^0.2.3", "@hookform/resolvers": "^5.2.0", "@prisma/client": "^6.12.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.52.1", "@types/react-syntax-highlighter": "^15.5.13", "@uiw/react-md-editor": "^4.0.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.526.0", "next": "15.4.4", "next-themes": "^0.4.6", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-breaks": "^4.0.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.6", "typescript": "^5"}}
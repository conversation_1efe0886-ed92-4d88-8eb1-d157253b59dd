import { 
  Folder, 
  Briefcase, 
  BookOpen, 
  Lightbulb, 
  Code, 
  Home, 
  Heart, 
  Star, 
  Tag, 
  Settings,
  FolderOpen
} from 'lucide-react'

// 图标映射表
export const ICON_MAP = {
  'folder': Folder,
  'briefcase': Briefcase,
  'book-open': BookOpen,
  'lightbulb': Lightbulb,
  'code': Code,
  'home': Home,
  'heart': Heart,
  'star': Star,
  'tag': Tag,
  'settings': Settings,
} as const

// 图标配置
export const PRESET_ICONS = [
  { value: "folder", label: "文件夹", icon: "📁" },
  { value: "briefcase", label: "工作", icon: "💼" },
  { value: "book-open", label: "学习", icon: "📖" },
  { value: "lightbulb", label: "创意", icon: "💡" },
  { value: "code", label: "编程", icon: "💻" },
  { value: "home", label: "生活", icon: "🏠" },
  { value: "heart", label: "收藏", icon: "❤️" },
  { value: "star", label: "重要", icon: "⭐" },
  { value: "tag", label: "标签", icon: "🏷️" },
  { value: "settings", label: "设置", icon: "⚙️" },
] as const

// 获取图标组件
export function getIconComponent(iconName: string, isOpen = false) {
  // 如果是选中状态且是文件夹图标，显示打开的文件夹
  if (isOpen && iconName === 'folder') {
    return FolderOpen
  }
  
  // 返回对应的图标组件，如果找不到则返回默认的文件夹图标
  return ICON_MAP[iconName as keyof typeof ICON_MAP] || Folder
}

// 获取图标的emoji表示
export function getIconEmoji(iconName: string) {
  const iconConfig = PRESET_ICONS.find(icon => icon.value === iconName)
  return iconConfig?.icon || "📁"
}

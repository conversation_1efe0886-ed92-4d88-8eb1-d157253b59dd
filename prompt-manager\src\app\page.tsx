"use client";

import { useEffect } from "react";
import { usePromptStore } from "@/lib/store";
import { promptsApi, categoriesApi, tagsApi } from "@/lib/api";
import { AppSidebar } from "@/components/app-sidebar";
import { PromptGrid } from "@/components/prompt-grid";
import { SearchBar } from "@/components/search-bar";
import { PromptDialog } from "@/components/prompt-dialog";
import { CategoryDialog } from "@/components/category-dialog";
import { TagDialog } from "@/components/tag-dialog";
import { BatchToolbar } from "@/components/batch-toolbar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Plus, CheckSquare } from "lucide-react";
import { toast } from "sonner";

export default function Home() {
  const {
    setPrompts,
    setCategories,
    setTags,
    setIsLoading,
    openPromptDialog,
    isLoading,
    // 🚀 批量操作相关状态
    isBatchMode,
    setBatchMode,
    selectedPromptIds
  } = usePromptStore();

  // 加载初始数据 - 使用并行加载优化性能
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        console.log("开始并行加载数据...");

        // 🚀 并行加载所有数据，大幅提升加载速度
        // 从串行加载改为并行加载，预计提升 60-70% 性能
        const [categoriesData, tagsData, promptsData] = await Promise.all([
          categoriesApi.getAll(),
          tagsApi.getAll(),
          promptsApi.getAll()
        ]);

        console.log("所有数据加载完成，开始更新状态");

        // 批量更新状态，减少重新渲染次数
        setCategories(categoriesData || []);
        setTags(tagsData || []);
        setPrompts(promptsData || []);

        console.log("数据加载和状态更新完成");
      } catch (error) {
        console.error("加载数据失败:", error);
        toast.error(`加载数据失败: ${error.message}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [setPrompts, setCategories, setTags, setIsLoading]);

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full relative overflow-hidden">


        {/* 侧边栏 */}
        <AppSidebar />

        {/* 主内容区 */}
        <main className="flex-1 flex flex-col overflow-hidden relative z-10">
          {/* 顶部工具栏 */}
          <header className="glass-effect border-b border-border/30 backdrop-blur-xl">
            <div className="flex h-16 items-center justify-between px-4 md:px-6">
              <div className="flex items-center gap-4 flex-1">
                <SearchBar />
              </div>

              <div className="flex items-center gap-2">
                {/* 🚀 批量模式切换按钮 */}
                <Button
                  variant={isBatchMode ? "default" : "outline"}
                  size="sm"
                  onClick={() => setBatchMode(!isBatchMode)}
                  className="gap-2 shrink-0 transition-all duration-200"
                >
                  <CheckSquare className="h-4 w-4" />
                  <span className="hidden sm:inline">
                    {isBatchMode ? `批量 (${selectedPromptIds.length})` : "批量"}
                  </span>
                </Button>

                <Button
                  onClick={() => {
                    console.log("新建提示词按钮被点击");
                    openPromptDialog();
                  }}
                  className="bg-primary text-primary-foreground hover:bg-primary/90 gap-2 shrink-0 transition-all duration-200"
                  size="sm"
                >
                  <Plus className="h-4 w-4" />
                  <span className="hidden sm:inline">新建提示词</span>
                  <span className="sm:hidden">新建</span>
                </Button>
              </div>
            </div>
          </header>

          {/* 内容区域 */}
          <div className="flex-1 overflow-auto p-4 md:p-6">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="flex flex-col items-center gap-4 animate-fade-in">
                  <div className="w-8 h-8 border-2 border-primary/30 border-t-primary rounded-full animate-spin" />
                  <div className="text-muted-foreground font-medium">加载中...</div>
                </div>
              </div>
            ) : (
              <div className="animate-fade-in">
                <PromptGrid />
              </div>
            )}
          </div>
        </main>

        {/* 🚀 批量操作工具栏 */}
        <BatchToolbar />

        {/* 提示词对话框 */}
        <PromptDialog />

        {/* 分类管理对话框 */}
        <CategoryDialog />

        {/* 标签管理对话框 */}
        <TagDialog />
      </div>
    </SidebarProvider>
  );
}

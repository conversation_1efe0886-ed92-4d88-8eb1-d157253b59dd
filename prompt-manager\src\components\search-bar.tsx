"use client";

import { useState, useEffect } from "react";
import { usePromptStore } from "@/lib/store";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  X,
  SlidersHorizontal
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

export function SearchBar() {
  const {
    searchQuery,
    setSearchQuery,
    selectedCategory,
    selectedTags,
    categories,
    tags,
    setSelectedCategory,
    setSelectedTags,
    filteredPrompts,
  } = usePromptStore();

  const [localQuery, setLocalQuery] = useState(searchQuery);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // 防抖搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchQuery(localQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [localQuery, setSearchQuery]);

  // 清除搜索
  const clearSearch = () => {
    setLocalQuery("");
    setSearchQuery("");
  };

  // 清除所有筛选
  const clearAllFilters = () => {
    setSelectedCategory(null);
    setSelectedTags([]);
    clearSearch();
  };

  // 获取选中的分类名称
  const getSelectedCategoryName = () => {
    if (!selectedCategory) return null;
    return categories.find(cat => cat.id === selectedCategory)?.name;
  };



  // 计算活跃筛选器数量
  const activeFiltersCount = 
    (selectedCategory ? 1 : 0) + 
    selectedTags.length + 
    (searchQuery ? 1 : 0);

  return (
    <div className="flex items-center gap-2 md:gap-3 flex-1 max-w-2xl">
      {/* 搜索输入框 */}
      <div className="relative flex-1">
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 p-1 rounded-md bg-gradient-to-r from-blue-500/20 to-purple-500/20">
          <Search className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
        </div>
        <Input
          placeholder="🔍 搜索标题、内容、描述、分类、标签..."
          value={localQuery}
          onChange={(e) => setLocalQuery(e.target.value)}
          className={cn(
            "modern-input pl-12 pr-10 text-sm md:text-base",
            "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm",
            "border-border/50 focus:border-primary/50",
            "shadow-sm hover:shadow-md transition-all duration-300",
            "placeholder:text-muted-foreground/60"
          )}
          title="支持全文搜索：标题、内容、描述、分类名称、标签名称"
        />
        {localQuery && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearSearch}
            className={cn(
              "absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 rounded-full",
              "hover:bg-red-500/20 hover:text-red-600 transition-all duration-200",
              "hover:scale-110"
            )}
          >
            <X className="h-3.5 w-3.5" />
          </Button>
        )}
      </div>

      {/* 筛选器按钮 */}
      <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "gap-1 md:gap-2 shrink-0 rounded-xl transition-all duration-300",
              "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm",
              "border-border/50 hover:border-primary/50",
              "shadow-sm hover:shadow-md hover:scale-105",
              activeFiltersCount > 0 && "border-primary/60 bg-gradient-to-r from-blue-500/10 to-purple-500/10"
            )}
          >
            <SlidersHorizontal className="h-4 w-4" />
            <span className="hidden sm:inline font-medium">筛选</span>
            {activeFiltersCount > 0 && (
              <Badge
                variant="secondary"
                className="ml-1 h-5 min-w-5 text-xs font-bold bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0"
              >
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 glass-effect border-border/50 shadow-xl" align="end">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">筛选条件</h4>
              {activeFiltersCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="h-auto p-1 text-xs"
                >
                  清除全部
                </Button>
              )}
            </div>

            {/* 分类筛选 */}
            <div>
              <label className="text-sm font-medium mb-2 block">分类</label>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <Badge
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => 
                      setSelectedCategory(
                        selectedCategory === category.id ? null : category.id
                      )
                    }
                  >
                    {category.name}
                  </Badge>
                ))}
              </div>
            </div>

            {/* 标签筛选 */}
            <div>
              <label className="text-sm font-medium mb-2 block">标签</label>
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Badge
                    key={tag.id}
                    variant={selectedTags.includes(tag.id) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => {
                      const newSelectedTags = selectedTags.includes(tag.id)
                        ? selectedTags.filter(id => id !== tag.id)
                        : [...selectedTags, tag.id];
                      setSelectedTags(newSelectedTags);
                    }}
                  >
                    #{tag.name}
                  </Badge>
                ))}
              </div>
            </div>

            {/* 结果统计 */}
            <div className="pt-2 border-t">
              <div className="text-sm text-muted-foreground">
                找到 <span className="font-medium">{filteredPrompts().length}</span> 个提示词
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* 活跃筛选器显示 */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center gap-2">
          {/* 分类筛选器 */}
          {selectedCategory && (
            <Badge variant="secondary" className="gap-1">
              分类: {getSelectedCategoryName()}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => setSelectedCategory(null)}
              />
            </Badge>
          )}

          {/* 标签筛选器 */}
          {selectedTags.map((tagId) => {
            const tag = tags.find(t => t.id === tagId);
            if (!tag) return null;
            return (
              <Badge key={tagId} variant="secondary" className="gap-1">
                #{tag.name}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => {
                    setSelectedTags(selectedTags.filter(id => id !== tagId));
                  }}
                />
              </Badge>
            );
          })}
        </div>
      )}
    </div>
  );
}

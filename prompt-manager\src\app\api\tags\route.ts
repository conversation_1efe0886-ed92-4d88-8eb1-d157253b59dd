import { NextRequest, NextResponse } from 'next/server'
import { tagsApi } from '@/lib/database'
import { withCache, createCachedResponse, invalidateCache, CACHE_KEYS, CACHE_CONFIG } from '@/lib/cache'

// GET /api/tags - 获取所有标签（带缓存优化）
export async function GET() {
  try {
    // 使用缓存装饰器，5分钟缓存
    const getCachedTags = withCache(
      CACHE_KEYS.TAGS,
      CACHE_CONFIG.READ_TTL.TAGS
    )(async () => {
      return await tagsApi.getAll();
    });

    const tags = await getCachedTags();

    // 返回带缓存头的响应
    return createCachedResponse(tags, CACHE_CONFIG.READ_TTL.TAGS);
  } catch (error) {
    console.error('获取标签失败:', error)
    return NextResponse.json(
      { error: '获取标签失败' },
      { status: 500 }
    )
  }
}

// POST /api/tags - 创建新标签（带缓存失效）
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, color } = body

    if (!name) {
      return NextResponse.json(
        { error: '标签名称不能为空' },
        { status: 400 }
      )
    }

    const tag = await tagsApi.create({
      name,
      color
    })

    // 创建成功后，清除相关缓存
    invalidateCache(CACHE_CONFIG.INVALIDATION_PATTERNS.TAGS);

    return NextResponse.json(tag)
  } catch (error) {
    console.error('创建标签失败:', error)
    return NextResponse.json(
      { error: '创建标签失败' },
      { status: 500 }
    )
  }
}

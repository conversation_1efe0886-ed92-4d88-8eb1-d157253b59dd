import { NextRequest, NextResponse } from 'next/server'
import { promptsApi } from '@/lib/database'
import { withCache, createCachedResponse, CACHE_KEYS, CACHE_CONFIG } from '@/lib/cache'

// GET /api/prompts/search - 全文搜索提示词
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const keyword = searchParams.get('q') || searchParams.get('keyword') || ''
    
    if (!keyword.trim()) {
      return NextResponse.json(
        { error: '搜索关键词不能为空' },
        { status: 400 }
      )
    }

    // 为搜索结果生成缓存键（包含关键词）
    const cacheKey = `${CACHE_KEYS.PROMPTS}:search:${encodeURIComponent(keyword.toLowerCase())}`;
    
    // 使用缓存装饰器，搜索结果缓存时间较短（30秒）
    const getCachedSearchResults = withCache(
      cacheKey,
      30 // 搜索结果缓存30秒
    )(async () => {
      return await promptsApi.search(keyword);
    });

    const prompts = await getCachedSearchResults();
    
    // 返回带缓存头的响应
    return createCachedResponse({
      prompts,
      keyword,
      total: prompts.length
    }, 30);
  } catch (error) {
    console.error('搜索提示词失败:', error)
    return NextResponse.json(
      { error: '搜索失败' },
      { status: 500 }
    )
  }
}

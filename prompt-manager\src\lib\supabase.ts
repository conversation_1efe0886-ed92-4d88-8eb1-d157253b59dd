import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 数据库类型定义
export interface Category {
  id: string
  name: string
  color: string
  icon: string
  parent_id: string | null
  sort_order: number
  created_at: string
}

export interface Tag {
  id: string
  name: string
  color: string
  created_at: string
}

export interface Prompt {
  id: string
  title: string
  content: string
  description: string | null
  category_id: string | null
  usage_count: number
  created_at: string
  updated_at: string
  category?: Category
  tags?: Tag[]
}

export interface PromptTag {
  prompt_id: string
  tag_id: string
}

// 数据库操作函数
export const promptsApi = {
  // 获取所有提示词
  async getAll() {
    try {
      // 先获取基础提示词数据
      const { data: prompts, error: promptsError } = await supabase
        .from('prompts')
        .select('*')
        .order('updated_at', { ascending: false })

      if (promptsError) throw promptsError

      // 获取所有分类
      const { data: categories, error: categoriesError } = await supabase
        .from('categories')
        .select('*')

      if (categoriesError) throw categoriesError

      // 获取所有标签关联
      const { data: promptTags, error: tagsError } = await supabase
        .from('prompt_tags')
        .select(`
          prompt_id,
          tag_id,
          tags(*)
        `)

      if (tagsError) throw tagsError

      // 手动组装数据
      const transformedData = prompts?.map(prompt => {
        const category = categories?.find(cat => cat.id === prompt.category_id) || null
        const tags = promptTags?.filter(pt => pt.prompt_id === prompt.id).map(pt => pt.tags) || []

        return {
          ...prompt,
          category,
          tags
        }
      })

      return transformedData || []
    } catch (error) {
      console.error('获取提示词失败:', error)
      throw error
    }
  },

  // 根据ID获取提示词
  async getById(id: string) {
    try {
      // 获取提示词基础数据
      const { data: prompt, error: promptError } = await supabase
        .from('prompts')
        .select('*')
        .eq('id', id)
        .single()

      if (promptError) throw promptError

      // 获取分类数据
      let category = null
      if (prompt.category_id) {
        const { data: categoryData, error: categoryError } = await supabase
          .from('categories')
          .select('*')
          .eq('id', prompt.category_id)
          .single()

        if (!categoryError) {
          category = categoryData
        }
      }

      // 获取标签数据
      const { data: promptTags, error: tagsError } = await supabase
        .from('prompt_tags')
        .select(`
          tag_id,
          tags(*)
        `)
        .eq('prompt_id', id)

      const tags = tagsError ? [] : promptTags?.map(pt => pt.tags) || []

      return {
        ...prompt,
        category,
        tags
      }
    } catch (error) {
      console.error('获取提示词详情失败:', error)
      throw error
    }
  },

  // 创建提示词
  async create(prompt: Omit<Prompt, 'id' | 'created_at' | 'updated_at' | 'usage_count'>) {
    const { data, error } = await supabase
      .from('prompts')
      .insert(prompt)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // 更新提示词
  async update(id: string, prompt: Partial<Prompt>) {
    const { data, error } = await supabase
      .from('prompts')
      .update({ ...prompt, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // 删除提示词
  async delete(id: string) {
    const { error } = await supabase
      .from('prompts')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  },

  // 增加使用次数
  async incrementUsage(id: string) {
    // 先获取当前的使用次数
    const { data: currentData, error: fetchError } = await supabase
      .from('prompts')
      .select('usage_count')
      .eq('id', id)
      .single()

    if (fetchError) throw fetchError

    // 更新使用次数
    const { data, error } = await supabase
      .from('prompts')
      .update({
        usage_count: (currentData.usage_count || 0) + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // 搜索提示词
  async search(query: string) {
    const { data, error } = await supabase
      .from('prompts')
      .select(`
        *,
        categories(*),
        prompt_tags(tags(*))
      `)
      .or(`title.ilike.%${query}%,content.ilike.%${query}%,description.ilike.%${query}%`)
      .order('updated_at', { ascending: false })

    if (error) throw error

    // 转换数据格式
    const transformedData = data?.map(prompt => ({
      ...prompt,
      category: prompt.categories,
      tags: prompt.prompt_tags?.map((pt: { tags: { id: string; name: string; color: string } }) => pt.tags) || []
    }))

    return transformedData
  },

  // 按分类筛选
  async getByCategory(categoryId: string) {
    const { data, error } = await supabase
      .from('prompts')
      .select(`
        *,
        categories(*),
        prompt_tags(tags(*))
      `)
      .eq('category_id', categoryId)
      .order('updated_at', { ascending: false })

    if (error) throw error

    // 转换数据格式
    const transformedData = data?.map(prompt => ({
      ...prompt,
      category: prompt.categories,
      tags: prompt.prompt_tags?.map((pt: { tags: { id: string; name: string; color: string } }) => pt.tags) || []
    }))

    return transformedData
  }
}

export const categoriesApi = {
  // 获取所有分类
  async getAll() {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('sort_order', { ascending: true })
    
    if (error) throw error
    return data
  },

  // 创建分类
  async create(category: Omit<Category, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('categories')
      .insert(category)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // 更新分类
  async update(id: string, category: Partial<Category>) {
    const { data, error } = await supabase
      .from('categories')
      .update(category)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // 删除分类
  async delete(id: string) {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}

export const tagsApi = {
  // 获取所有标签
  async getAll() {
    const { data, error } = await supabase
      .from('tags')
      .select('*')
      .order('name', { ascending: true })
    
    if (error) throw error
    return data
  },

  // 创建标签
  async create(tag: Omit<Tag, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('tags')
      .insert(tag)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // 更新标签
  async update(id: string, tag: Partial<Tag>) {
    const { data, error } = await supabase
      .from('tags')
      .update(tag)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // 删除标签
  async delete(id: string) {
    const { error } = await supabase
      .from('tags')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  // 为提示词添加标签
  async addToPrompt(promptId: string, tagId: string) {
    const { error } = await supabase
      .from('prompt_tags')
      .insert({ prompt_id: promptId, tag_id: tagId })
    
    if (error) throw error
  },

  // 从提示词移除标签
  async removeFromPrompt(promptId: string, tagId: string) {
    const { error } = await supabase
      .from('prompt_tags')
      .delete()
      .eq('prompt_id', promptId)
      .eq('tag_id', tagId)
    
    if (error) throw error
  }
}

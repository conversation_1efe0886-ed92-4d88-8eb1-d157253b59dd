-- 提示词管理工具数据库架构
-- 在 Supabase SQL 编辑器中执行此脚本

-- 启用 UUID 扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 分类表
CREATE TABLE IF NOT EXISTS categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  color VARCHAR(7) DEFAULT '#3B82F6',
  icon VARCHAR(50) DEFAULT 'folder',
  parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 标签表
CREATE TABLE IF NOT EXISTS tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) NOT NULL UNIQUE,
  color VARCHAR(7) DEFAULT '#6B7280',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 提示词表
CREATE TABLE IF NOT EXISTS prompts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  description TEXT,
  category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 提示词标签关联表
CREATE TABLE IF NOT EXISTS prompt_tags (
  prompt_id UUID REFERENCES prompts(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
  PRIMARY KEY (prompt_id, tag_id)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_prompts_category_id ON prompts(category_id);
CREATE INDEX IF NOT EXISTS idx_prompts_title ON prompts(title);
CREATE INDEX IF NOT EXISTS idx_prompts_updated_at ON prompts(updated_at);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order);
CREATE INDEX IF NOT EXISTS idx_prompt_tags_prompt_id ON prompt_tags(prompt_id);
CREATE INDEX IF NOT EXISTS idx_prompt_tags_tag_id ON prompt_tags(tag_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 prompts 表创建更新时间触发器
CREATE TRIGGER update_prompts_updated_at 
    BEFORE UPDATE ON prompts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 插入默认分类数据
INSERT INTO categories (name, color, icon, sort_order) VALUES
('工作', '#3B82F6', 'briefcase', 1),
('学习', '#10B981', 'book-open', 2),
('创意', '#F59E0B', 'lightbulb', 3),
('编程', '#8B5CF6', 'code', 4),
('生活', '#EF4444', 'home', 5)
ON CONFLICT DO NOTHING;

-- 插入默认标签数据
INSERT INTO tags (name, color) VALUES
('重要', '#EF4444'),
('常用', '#F59E0B'),
('模板', '#10B981'),
('草稿', '#6B7280'),
('收藏', '#F97316')
ON CONFLICT DO NOTHING;

-- 插入示例提示词数据
INSERT INTO prompts (title, content, description, category_id) VALUES
(
  '代码审查提示词',
  '请帮我审查以下代码，重点关注：
1. 代码质量和可读性
2. 潜在的性能问题
3. 安全漏洞
4. 最佳实践建议

代码：
```
[在此处粘贴代码]
```',
  '用于代码审查的通用提示词模板',
  (SELECT id FROM categories WHERE name = '编程' LIMIT 1)
),
(
  '文章写作助手',
  '请帮我写一篇关于 [主题] 的文章，要求：
1. 字数约 [字数] 字
2. 目标读者：[读者群体]
3. 写作风格：[风格要求]
4. 包含以下要点：
   - [要点1]
   - [要点2]
   - [要点3]

请确保文章结构清晰，逻辑连贯，语言生动有趣。',
  '通用的文章写作提示词模板',
  (SELECT id FROM categories WHERE name = '创意' LIMIT 1)
),
(
  '学习计划制定',
  '请帮我制定一个关于 [学习主题] 的学习计划：

学习目标：[具体目标]
可用时间：[每天/每周时间]
当前水平：[初级/中级/高级]
学习期限：[时间期限]

请提供：
1. 详细的学习路径
2. 每个阶段的学习重点
3. 推荐的学习资源
4. 进度检查方式
5. 实践项目建议',
  '制定个性化学习计划的提示词',
  (SELECT id FROM categories WHERE name = '学习' LIMIT 1)
)
ON CONFLICT DO NOTHING;

-- 为示例提示词添加标签
INSERT INTO prompt_tags (prompt_id, tag_id)
SELECT p.id, t.id
FROM prompts p, tags t
WHERE p.title = '代码审查提示词' AND t.name = '常用'
ON CONFLICT DO NOTHING;

INSERT INTO prompt_tags (prompt_id, tag_id)
SELECT p.id, t.id
FROM prompts p, tags t
WHERE p.title = '文章写作助手' AND t.name = '模板'
ON CONFLICT DO NOTHING;

INSERT INTO prompt_tags (prompt_id, tag_id)
SELECT p.id, t.id
FROM prompts p, tags t
WHERE p.title = '学习计划制定' AND t.name = '重要'
ON CONFLICT DO NOTHING;

-- 设置行级安全策略（RLS）
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE prompts ENABLE ROW LEVEL SECURITY;
ALTER TABLE prompt_tags ENABLE ROW LEVEL SECURITY;

-- 创建允许所有操作的策略（适用于公开应用）
-- 注意：在生产环境中，您可能需要更严格的安全策略

CREATE POLICY "Allow all operations on categories" ON categories
FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on tags" ON tags
FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on prompts" ON prompts
FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on prompt_tags" ON prompt_tags
FOR ALL USING (true) WITH CHECK (true);

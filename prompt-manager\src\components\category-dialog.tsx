"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { usePromptStore, useCategoryStore } from "@/lib/store";
import { categoriesApi } from "@/lib/api";
import type { Category } from "@/lib/api";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Save, 
  Palette,
  Folder
} from "lucide-react";
import { toast } from "sonner";
import { PRESET_ICONS, getIconComponent } from "@/lib/icons";

// 表单验证模式
const categorySchema = z.object({
  name: z.string().min(1, "分类名称不能为空").max(100, "分类名称过长"),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, "请选择有效的颜色"),
  icon: z.string().default("folder"),
  parentId: z.string().optional(),
  sortOrder: z.number().default(0),
});

type CategoryFormData = z.infer<typeof categorySchema>;

// 预设颜色
const PRESET_COLORS = [
  "#3B82F6", // 蓝色
  "#10B981", // 绿色
  "#F59E0B", // 黄色
  "#EF4444", // 红色
  "#8B5CF6", // 紫色
  "#F97316", // 橙色
  "#06B6D4", // 青色
  "#84CC16", // 青绿色
  "#EC4899", // 粉色
  "#6B7280", // 灰色
];



export function CategoryDialog() {
  const {
    categories,
    setCategories,
    prompts,
    setPrompts,
  } = usePromptStore();

  const {
    isCategoryDialogOpen,
    selectedCategoryForEdit,
    closeCategoryDialog,
  } = useCategoryStore();

  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",
      color: PRESET_COLORS[0],
      icon: "folder",
      parentId: "none",
      sortOrder: 0,
    },
  });

  // 当选中的分类改变时，更新表单
  useEffect(() => {
    if (selectedCategoryForEdit) {
      reset({
        name: selectedCategoryForEdit.name,
        color: selectedCategoryForEdit.color,
        icon: selectedCategoryForEdit.icon,
        parentId: selectedCategoryForEdit.parentId || "none",
        sortOrder: selectedCategoryForEdit.sortOrder,
      });
    } else {
      reset({
        name: "",
        color: PRESET_COLORS[0],
        icon: "folder",
        parentId: "none",
        sortOrder: categories.length,
      });
    }
  }, [selectedCategoryForEdit, reset, categories.length]);

  // 提交表单
  const onSubmit = async (data: CategoryFormData) => {
    try {
      setIsLoading(true);

      const categoryData = {
        ...data,
        parentId: data.parentId === "none" || !data.parentId ? null : data.parentId,
      };

      if (selectedCategoryForEdit) {
        // 更新现有分类
        const updatedCategory = await categoriesApi.update(
          selectedCategoryForEdit.id,
          categoryData
        );

        // 更新本地状态
        const updatedCategories = categories.map(cat =>
          cat.id === selectedCategoryForEdit.id ? updatedCategory : cat
        );
        setCategories(updatedCategories);

        // 同时更新相关提示词的分类信息
        const updatedPrompts = prompts.map(prompt =>
          prompt.categoryId === selectedCategoryForEdit.id
            ? { ...prompt, category: updatedCategory }
            : prompt
        );
        setPrompts(updatedPrompts);

        toast.success("分类已更新");
      } else {
        // 创建新分类
        const newCategory = await categoriesApi.create(categoryData);

        // 添加到本地状态
        setCategories([...categories, newCategory]);

        toast.success("分类已创建");
      }

      closeCategoryDialog();
    } catch (error) {
      console.error("保存分类失败:", error);
      toast.error("保存分类失败");
    } finally {
      setIsLoading(false);
    }
  };

  // 获取可选的父分类（排除自己和子分类）
  const getAvailableParentCategories = () => {
    if (!selectedCategoryForEdit) return categories;
    
    // 排除自己
    return categories.filter(cat => cat.id !== selectedCategoryForEdit.id);
  };

  return (
    <Dialog open={isCategoryDialogOpen} onOpenChange={closeCategoryDialog}>
      <DialogContent className="max-w-md bg-slate-50 border-4 border-slate-800 shadow-2xl ring-8 ring-primary/20">
        <DialogHeader>
          <DialogTitle>
            {selectedCategoryForEdit ? "编辑分类" : "新建分类"}
          </DialogTitle>
          <DialogDescription>
            {selectedCategoryForEdit ? "修改分类的名称、颜色和图标" : "创建一个新的分类来组织你的提示词"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* 分类名称 */}
          <div className="space-y-2">
            <Label htmlFor="name">分类名称 *</Label>
            <Input
              id="name"
              placeholder="输入分类名称..."
              {...register("name")}
            />
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>

          {/* 颜色选择 */}
          <div className="space-y-2">
            <Label>颜色</Label>
            <div className="flex flex-wrap gap-2">
              {PRESET_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  className={`w-8 h-8 rounded-full border-2 transition-all ${
                    watch("color") === color 
                      ? "border-foreground scale-110" 
                      : "border-muted hover:scale-105"
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => setValue("color", color)}
                />
              ))}
            </div>
            <Input
              type="color"
              value={watch("color")}
              onChange={(e) => setValue("color", e.target.value)}
              className="w-20 h-10"
            />
          </div>

          {/* 图标选择 */}
          <div className="space-y-2">
            <Label>图标</Label>
            <Select
              value={watch("icon")}
              onValueChange={(value) => setValue("icon", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {PRESET_ICONS.map((icon) => (
                  <SelectItem key={icon.value} value={icon.value}>
                    <div className="flex items-center gap-2">
                      <span>{icon.icon}</span>
                      <span>{icon.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 父分类 */}
          <div className="space-y-2">
            <Label>父分类</Label>
            <Select
              value={watch("parentId") || "none"}
              onValueChange={(value) => setValue("parentId", value === "none" ? "" : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择父分类（可选）" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">无父分类</SelectItem>
                {getAvailableParentCategories().map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                      {category.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 预览 */}
          <div className="space-y-2">
            <Label>预览</Label>
            <div className="flex items-center gap-2 p-3 border rounded-lg">
              {(() => {
                const IconComponent = getIconComponent(watch("icon"));
                return <IconComponent
                  className="h-4 w-4"
                  style={{ color: watch("color") }}
                />;
              })()}
              <span>{watch("name") || "分类名称"}</span>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={closeCategoryDialog}
              disabled={isLoading}
            >
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                "保存中..."
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {selectedCategoryForEdit ? "更新" : "创建"}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

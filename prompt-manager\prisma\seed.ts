import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('开始初始化数据库...')

  // 创建默认分类
  const categories = await Promise.all([
    prisma.category.create({
      data: {
        name: '工作效率',
        color: '#3B82F6',
        icon: 'briefcase',
        sortOrder: 1
      }
    }),
    prisma.category.create({
      data: {
        name: '创意写作',
        color: '#10B981',
        icon: 'pen',
        sortOrder: 2
      }
    }),
    prisma.category.create({
      data: {
        name: '编程开发',
        color: '#8B5CF6',
        icon: 'code',
        sortOrder: 3
      }
    }),
    prisma.category.create({
      data: {
        name: '学习教育',
        color: '#F59E0B',
        icon: 'book',
        sortOrder: 4
      }
    })
  ])

  // 创建默认标签
  const tags = await Promise.all([
    prisma.tag.create({
      data: {
        name: '常用',
        color: '#EF4444'
      }
    }),
    prisma.tag.create({
      data: {
        name: '重要',
        color: '#F59E0B'
      }
    }),
    prisma.tag.create({
      data: {
        name: '模板',
        color: '#10B981'
      }
    }),
    prisma.tag.create({
      data: {
        name: '示例',
        color: '#3B82F6'
      }
    })
  ])

  // 创建示例提示词
  const prompts = await Promise.all([
    prisma.prompt.create({
      data: {
        title: '文章写作助手',
        description: '帮助用户写出高质量的文章',
        content: `你是一个专业的文章写作助手。请根据用户提供的主题和要求，帮助他们写出结构清晰、内容丰富的文章。

请按照以下步骤进行：

1. **理解主题**：仔细分析用户的写作需求和目标受众
2. **构建大纲**：提供清晰的文章结构和要点
3. **内容创作**：写出引人入胜的开头、充实的正文和有力的结尾
4. **优化建议**：提供改进文章质量的具体建议

请确保文章：
- 逻辑清晰，层次分明
- 语言流畅，表达准确
- 内容有价值，观点鲜明
- 符合目标受众的阅读习惯`,
        categoryId: categories[1].id, // 创意写作
        promptTags: {
          create: [
            { tagId: tags[0].id }, // 常用
            { tagId: tags[2].id }  // 模板
          ]
        }
      }
    }),
    prisma.prompt.create({
      data: {
        title: '代码审查助手',
        description: '专业的代码审查和优化建议',
        content: `你是一个经验丰富的代码审查专家。请对用户提供的代码进行全面的审查和分析。

审查重点：

## 🔍 代码质量
- **可读性**：变量命名、注释、代码结构
- **性能**：算法效率、资源使用
- **安全性**：潜在的安全漏洞和风险
- **可维护性**：代码组织、模块化程度

## 📋 审查流程
1. 理解代码功能和业务逻辑
2. 检查代码规范和最佳实践
3. 识别潜在的bug和问题
4. 提供具体的改进建议
5. 推荐更好的实现方案

## 💡 输出格式
- **优点**：代码中做得好的地方
- **问题**：发现的问题和风险
- **建议**：具体的改进方案
- **示例**：提供优化后的代码示例`,
        categoryId: categories[2].id, // 编程开发
        promptTags: {
          create: [
            { tagId: tags[1].id }, // 重要
            { tagId: tags[2].id }  // 模板
          ]
        }
      }
    })
  ])

  console.log('数据库初始化完成！')
  console.log(`创建了 ${categories.length} 个分类`)
  console.log(`创建了 ${tags.length} 个标签`)
  console.log(`创建了 ${prompts.length} 个示例提示词`)
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

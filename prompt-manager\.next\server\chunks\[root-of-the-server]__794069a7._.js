module.exports = {

"[project]/.next-internal/server/app/api/prompts/[id]/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "prisma": ()=>prisma
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
    log: [
        'query'
    ]
});
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "categoriesApi": ()=>categoriesApi,
    "promptsApi": ()=>promptsApi,
    "tagsApi": ()=>tagsApi
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
const promptsApi = {
    // 获取所有提示词（智能排序：置顶优先，然后按使用频率排序）
    async getAll () {
        const prompts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.findMany({
            include: {
                category: true,
                promptTags: {
                    include: {
                        tag: true
                    }
                }
            },
            orderBy: [
                {
                    isPinned: 'desc'
                },
                {
                    pinnedAt: 'desc'
                },
                {
                    usageCount: 'desc'
                },
                {
                    updatedAt: 'desc'
                } // 最后更新时间倒序
            ]
        });
        // 转换数据格式以兼容现有代码
        return prompts.map((prompt)=>({
                ...prompt,
                tags: prompt.promptTags.map((pt)=>pt.tag)
            }));
    },
    // 根据 ID 获取提示词
    async getById (id) {
        const prompt = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.findUnique({
            where: {
                id
            },
            include: {
                category: true,
                promptTags: {
                    include: {
                        tag: true
                    }
                }
            }
        });
        if (!prompt) return null;
        // 转换数据格式以兼容现有代码
        return {
            ...prompt,
            tags: prompt.promptTags.map((pt)=>pt.tag)
        };
    },
    // 创建提示词
    async create (data) {
        const { tagIds, ...promptData } = data;
        const prompt = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.create({
            data: {
                ...promptData,
                promptTags: tagIds ? {
                    create: tagIds.map((tagId)=>({
                            tagId
                        }))
                } : undefined
            },
            include: {
                category: true,
                promptTags: {
                    include: {
                        tag: true
                    }
                }
            }
        });
        // 转换数据格式以兼容现有代码
        return {
            ...prompt,
            tags: prompt.promptTags.map((pt)=>pt.tag)
        };
    },
    // 更新提示词
    async update (id, data) {
        const { tagIds, ...promptData } = data;
        // 如果有标签更新，先删除现有关联，再创建新关联
        if (tagIds !== undefined) {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].promptTag.deleteMany({
                where: {
                    promptId: id
                }
            });
        }
        const prompt = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.update({
            where: {
                id
            },
            data: {
                ...promptData,
                promptTags: tagIds ? {
                    create: tagIds.map((tagId)=>({
                            tagId
                        }))
                } : undefined
            },
            include: {
                category: true,
                promptTags: {
                    include: {
                        tag: true
                    }
                }
            }
        });
        // 转换数据格式以兼容现有代码
        return {
            ...prompt,
            tags: prompt.promptTags.map((pt)=>pt.tag)
        };
    },
    // 删除提示词
    async delete (id) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.delete({
            where: {
                id
            }
        });
    },
    // 增加使用次数
    async incrementUsage (id) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.update({
            where: {
                id
            },
            data: {
                usageCount: {
                    increment: 1
                }
            }
        });
    },
    // 置顶/取消置顶提示词
    async togglePin (id) {
        const prompt = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.findUnique({
            where: {
                id
            }
        });
        if (!prompt) {
            throw new Error('提示词不存在');
        }
        const updatedPrompt = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.update({
            where: {
                id
            },
            data: {
                isPinned: !prompt.isPinned,
                pinnedAt: !prompt.isPinned ? new Date() : null
            },
            include: {
                category: true,
                promptTags: {
                    include: {
                        tag: true
                    }
                }
            }
        });
        return {
            ...updatedPrompt,
            tags: updatedPrompt.promptTags.map((pt)=>pt.tag)
        };
    },
    // 设置置顶状态
    async setPin (id, isPinned) {
        const updatedPrompt = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.update({
            where: {
                id
            },
            data: {
                isPinned,
                pinnedAt: isPinned ? new Date() : null
            },
            include: {
                category: true,
                promptTags: {
                    include: {
                        tag: true
                    }
                }
            }
        });
        return {
            ...updatedPrompt,
            tags: updatedPrompt.promptTags.map((pt)=>pt.tag)
        };
    },
    // 按分类筛选（智能排序：置顶优先，然后按使用频率排序）
    async getByCategory (categoryId) {
        const prompts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.findMany({
            where: {
                categoryId
            },
            include: {
                category: true,
                promptTags: {
                    include: {
                        tag: true
                    }
                }
            },
            orderBy: [
                {
                    isPinned: 'desc'
                },
                {
                    pinnedAt: 'desc'
                },
                {
                    usageCount: 'desc'
                },
                {
                    updatedAt: 'desc'
                } // 最后更新时间倒序
            ]
        });
        // 转换数据格式以兼容现有代码
        return prompts.map((prompt)=>({
                ...prompt,
                tags: prompt.promptTags.map((pt)=>pt.tag)
            }));
    },
    // 全文搜索（标题+内容+描述）
    async search (keyword) {
        if (!keyword.trim()) {
            return this.getAll(); // 如果没有关键词，返回所有提示词
        }
        const prompts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.findMany({
            where: {
                OR: [
                    {
                        title: {
                            contains: keyword,
                            mode: 'insensitive'
                        }
                    },
                    {
                        content: {
                            contains: keyword,
                            mode: 'insensitive'
                        }
                    },
                    {
                        description: {
                            contains: keyword,
                            mode: 'insensitive'
                        }
                    }
                ]
            },
            include: {
                category: true,
                promptTags: {
                    include: {
                        tag: true
                    }
                }
            },
            orderBy: [
                {
                    isPinned: 'desc'
                },
                {
                    pinnedAt: 'desc'
                },
                {
                    usageCount: 'desc'
                },
                {
                    updatedAt: 'desc'
                } // 最后更新时间倒序
            ]
        });
        // 转换数据格式以兼容现有代码
        return prompts.map((prompt)=>({
                ...prompt,
                tags: prompt.promptTags.map((pt)=>pt.tag)
            }));
    },
    // 批量删除提示词
    async deleteMany (ids) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.deleteMany({
            where: {
                id: {
                    in: ids
                }
            }
        });
    },
    // 批量移动分类
    async updateCategory (ids, categoryId) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].prompt.updateMany({
            where: {
                id: {
                    in: ids
                }
            },
            data: {
                categoryId: categoryId === 'none' ? null : categoryId
            }
        });
    }
};
const categoriesApi = {
    // 获取所有分类
    async getAll () {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].category.findMany({
            include: {
                children: true
            },
            orderBy: {
                sortOrder: 'asc'
            }
        });
    },
    // 根据 ID 获取分类
    async getById (id) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].category.findUnique({
            where: {
                id
            }
        });
    },
    // 创建分类
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].category.create({
            data
        });
    },
    // 更新分类
    async update (id, data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].category.update({
            where: {
                id
            },
            data
        });
    },
    // 删除分类
    async delete (id) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].category.delete({
            where: {
                id
            }
        });
    }
};
const tagsApi = {
    // 获取所有标签
    async getAll () {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].tag.findMany({
            orderBy: {
                name: 'asc'
            }
        });
    },
    // 根据 ID 获取标签
    async getById (id) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].tag.findUnique({
            where: {
                id
            }
        });
    },
    // 创建标签
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].tag.create({
            data
        });
    },
    // 更新标签
    async update (id, data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].tag.update({
            where: {
                id
            },
            data
        });
    },
    // 删除标签
    async delete (id) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].tag.delete({
            where: {
                id
            }
        });
    }
};
}),
"[project]/src/app/api/prompts/[id]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "DELETE": ()=>DELETE,
    "GET": ()=>GET,
    "PUT": ()=>PUT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
async function GET(request, { params }) {
    try {
        const prompt = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promptsApi"].getById(params.id);
        if (!prompt) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '提示词不存在'
            }, {
                status: 404
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(prompt);
    } catch (error) {
        console.error('获取提示词失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '获取提示词失败'
        }, {
            status: 500
        });
    }
}
async function PUT(request, { params }) {
    try {
        const body = await request.json();
        const { title, content, description, categoryId, tagIds } = body;
        const prompt = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promptsApi"].update(params.id, {
            title,
            content,
            description,
            categoryId: categoryId === 'none' ? undefined : categoryId,
            tagIds
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(prompt);
    } catch (error) {
        console.error('更新提示词失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '更新提示词失败'
        }, {
            status: 500
        });
    }
}
async function DELETE(request, { params }) {
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promptsApi"].delete(params.id);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true
        });
    } catch (error) {
        console.error('删除提示词失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '删除提示词失败'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__794069a7._.js.map
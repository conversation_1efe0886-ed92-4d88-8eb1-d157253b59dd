import { NextRequest, NextResponse } from 'next/server'
import { promptsApi } from '@/lib/database'
import { withCache, createCachedResponse, invalidateCache, CACHE_KEYS, CACHE_CONFIG } from '@/lib/cache'

// GET /api/prompts - 获取所有提示词（带缓存优化）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('categoryId')

    // 根据查询参数生成不同的缓存键
    const cacheKey = categoryId && categoryId !== 'all'
      ? `${CACHE_KEYS.PROMPTS}:category:${categoryId}`
      : CACHE_KEYS.PROMPTS;

    // 使用缓存装饰器，1分钟缓存（提示词更新较频繁）
    const getCachedPrompts = withCache(
      cacheKey,
      CACHE_CONFIG.READ_TTL.PROMPTS
    )(async () => {
      if (categoryId && categoryId !== 'all') {
        return await promptsApi.getByCategory(categoryId);
      } else {
        return await promptsApi.getAll();
      }
    });

    const prompts = await getCachedPrompts();

    // 返回带缓存头的响应
    return createCachedResponse(prompts, CACHE_CONFIG.READ_TTL.PROMPTS);
  } catch (error) {
    console.error('获取提示词失败:', error)
    return NextResponse.json(
      { error: '获取提示词失败' },
      { status: 500 }
    )
  }
}

// POST /api/prompts - 创建新提示词（带缓存失效）
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, content, description, categoryId, tagIds } = body

    if (!title || !content) {
      return NextResponse.json(
        { error: '标题和内容不能为空' },
        { status: 400 }
      )
    }

    const prompt = await promptsApi.create({
      title,
      content,
      description,
      categoryId: categoryId === 'none' ? undefined : categoryId,
      tagIds
    })

    // 创建成功后，清除相关缓存
    invalidateCache(CACHE_CONFIG.INVALIDATION_PATTERNS.PROMPTS);

    return NextResponse.json(prompt)
  } catch (error) {
    console.error('创建提示词失败:', error)
    return NextResponse.json(
      { error: '创建提示词失败' },
      { status: 500 }
    )
  }
}

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/system/info - 获取系统和数据库信息
export async function GET() {
  try {
    // 获取数据库连接信息
    const databaseUrl = process.env.POSTGRES_PRISMA_URL || process.env.DATABASE_URL || 'Unknown'
    
    // 执行一个简单的数据库查询来验证连接
    const categoryCount = await prisma.category.count()
    const promptCount = await prisma.prompt.count()
    const tagCount = await prisma.tag.count()
    
    // 获取一个示例 ID 来检查格式
    const sampleCategory = await prisma.category.findFirst()
    
    const systemInfo = {
      database: {
        type: databaseUrl.includes('prisma-data.net') ? 'Vercel Postgres + Prisma Accelerate' : 
              databaseUrl.includes('supabase') ? 'Supabase' : 
              databaseUrl.includes('db.prisma.io') ? 'Vercel Postgres' : 'Unknown',
        connectionUrl: databaseUrl.substring(0, 50) + '...',
        isAccelerate: databaseUrl.includes('prisma-data.net'),
        isPrisma: true
      },
      data: {
        categories: categoryCount,
        prompts: promptCount,
        tags: tagCount,
        sampleIdFormat: sampleCategory?.id || 'No data'
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        vercel: !!process.env.VERCEL,
        timestamp: new Date().toISOString()
      }
    }
    
    return NextResponse.json(systemInfo)
  } catch (error) {
    console.error('获取系统信息失败:', error)
    return NextResponse.json(
      { 
        error: '获取系统信息失败',
        database: {
          type: 'Connection Failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      },
      { status: 500 }
    )
  }
}

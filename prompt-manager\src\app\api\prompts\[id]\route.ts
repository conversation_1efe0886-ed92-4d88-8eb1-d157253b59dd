import { NextRequest, NextResponse } from 'next/server'
import { promptsApi } from '@/lib/database'

// GET /api/prompts/[id] - 获取单个提示词
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const prompt = await promptsApi.getById(params.id)
    
    if (!prompt) {
      return NextResponse.json(
        { error: '提示词不存在' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(prompt)
  } catch (error) {
    console.error('获取提示词失败:', error)
    return NextResponse.json(
      { error: '获取提示词失败' },
      { status: 500 }
    )
  }
}

// PUT /api/prompts/[id] - 更新提示词
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { title, content, description, categoryId, tagIds } = body
    
    const prompt = await promptsApi.update(params.id, {
      title,
      content,
      description,
      categoryId: categoryId === 'none' ? undefined : categoryId,
      tagIds
    })
    
    return NextResponse.json(prompt)
  } catch (error) {
    console.error('更新提示词失败:', error)
    return NextResponse.json(
      { error: '更新提示词失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/prompts/[id] - 删除提示词
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await promptsApi.delete(params.id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('删除提示词失败:', error)
    return NextResponse.json(
      { error: '删除提示词失败' },
      { status: 500 }
    )
  }
}

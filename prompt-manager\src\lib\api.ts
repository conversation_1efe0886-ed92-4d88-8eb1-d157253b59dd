// 客户端 API 调用层 - 替代直接的数据库调用

export type Prompt = {
  id: string
  title: string
  content: string
  description?: string
  categoryId?: string
  usageCount: number
  // 🚀 新增置顶相关字段
  isPinned: boolean
  pinnedAt?: string | null
  createdAt: string
  updatedAt: string
  category?: Category | null
  tags: Tag[]
}

export type Category = {
  id: string
  name: string
  color: string
  icon: string
  parentId?: string
  sortOrder: number
  createdAt: string
}

export type Tag = {
  id: string
  name: string
  color: string
  createdAt: string
}

// 提示词 API
export const promptsApi = {
  // 获取所有提示词
  async getAll(): Promise<Prompt[]> {
    const response = await fetch('/api/prompts')
    if (!response.ok) {
      throw new Error('获取提示词失败')
    }
    return response.json()
  },

  // 根据 ID 获取提示词
  async getById(id: string): Promise<Prompt | null> {
    const response = await fetch(`/api/prompts/${id}`)
    if (response.status === 404) {
      return null
    }
    if (!response.ok) {
      throw new Error('获取提示词失败')
    }
    return response.json()
  },

  // 创建提示词
  async create(data: {
    title: string
    content: string
    description?: string
    categoryId?: string
    tagIds?: string[]
  }): Promise<Prompt> {
    const response = await fetch('/api/prompts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })
    if (!response.ok) {
      throw new Error('创建提示词失败')
    }
    return response.json()
  },

  // 更新提示词
  async update(id: string, data: {
    title?: string
    content?: string
    description?: string
    categoryId?: string
    tagIds?: string[]
  }): Promise<Prompt> {
    const response = await fetch(`/api/prompts/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })
    if (!response.ok) {
      throw new Error('更新提示词失败')
    }
    return response.json()
  },

  // 删除提示词
  async delete(id: string): Promise<void> {
    const response = await fetch(`/api/prompts/${id}`, {
      method: 'DELETE',
    })
    if (!response.ok) {
      throw new Error('删除提示词失败')
    }
  },

  // 增加使用次数
  async incrementUsage(id: string): Promise<void> {
    const response = await fetch(`/api/prompts/${id}/usage`, {
      method: 'POST',
    })
    if (!response.ok) {
      throw new Error('增加使用次数失败')
    }
  },

  // 按分类筛选
  async getByCategory(categoryId: string): Promise<Prompt[]> {
    const response = await fetch(`/api/prompts?categoryId=${categoryId}`)
    if (!response.ok) {
      throw new Error('获取提示词失败')
    }
    return response.json()
  },

  // 🚀 新增功能：增加使用次数
  async incrementUsage(id: string): Promise<void> {
    const response = await fetch(`/api/prompts/${id}/usage`, {
      method: 'POST'
    })
    if (!response.ok) {
      throw new Error('更新使用次数失败')
    }
  },

  // 🚀 新增功能：全文搜索
  async search(keyword: string): Promise<Prompt[]> {
    const response = await fetch(`/api/prompts/search?q=${encodeURIComponent(keyword)}`)
    if (!response.ok) {
      throw new Error('搜索失败')
    }
    const data = await response.json()
    return data.prompts || []
  },

  // 🚀 新增功能：置顶/取消置顶
  async togglePin(id: string): Promise<Prompt> {
    const response = await fetch(`/api/prompts/${id}/pin`, {
      method: 'POST'
    })
    if (!response.ok) {
      throw new Error('切换置顶状态失败')
    }
    const data = await response.json()
    return data.prompt
  },

  // 🚀 新增功能：批量删除
  async deleteMany(ids: string[]): Promise<void> {
    const response = await fetch('/api/prompts/batch', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ ids })
    })
    if (!response.ok) {
      throw new Error('批量删除失败')
    }
  },

  // 🚀 新增功能：批量移动分类
  async updateCategory(ids: string[], categoryId: string | null): Promise<void> {
    const response = await fetch('/api/prompts/batch', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ids,
        action: 'move_category',
        data: { categoryId }
      })
    })
    if (!response.ok) {
      throw new Error('批量移动分类失败')
    }
  }
}

// 分类 API
export const categoriesApi = {
  // 获取所有分类
  async getAll(): Promise<Category[]> {
    const response = await fetch('/api/categories')
    if (!response.ok) {
      throw new Error('获取分类失败')
    }
    return response.json()
  },

  // 创建分类
  async create(data: {
    name: string
    color?: string
    icon?: string
    parentId?: string
    sortOrder?: number
  }): Promise<Category> {
    const response = await fetch('/api/categories', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })
    if (!response.ok) {
      throw new Error('创建分类失败')
    }
    return response.json()
  },

  // 更新分类
  async update(id: string, data: {
    name?: string
    color?: string
    icon?: string
    parentId?: string
    sortOrder?: number
  }): Promise<Category> {
    const response = await fetch(`/api/categories/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })
    if (!response.ok) {
      throw new Error('更新分类失败')
    }
    return response.json()
  },

  // 删除分类
  async delete(id: string): Promise<void> {
    const response = await fetch(`/api/categories/${id}`, {
      method: 'DELETE',
    })
    if (!response.ok) {
      throw new Error('删除分类失败')
    }
  }
}

// 标签 API
export const tagsApi = {
  // 获取所有标签
  async getAll(): Promise<Tag[]> {
    const response = await fetch('/api/tags')
    if (!response.ok) {
      throw new Error('获取标签失败')
    }
    return response.json()
  },

  // 创建标签
  async create(data: {
    name: string
    color?: string
  }): Promise<Tag> {
    const response = await fetch('/api/tags', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })
    if (!response.ok) {
      throw new Error('创建标签失败')
    }
    return response.json()
  },

  // 更新标签
  async update(id: string, data: {
    name?: string
    color?: string
  }): Promise<Tag> {
    const response = await fetch(`/api/tags/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })
    if (!response.ok) {
      throw new Error('更新标签失败')
    }
    return response.json()
  },

  // 删除标签
  async delete(id: string): Promise<void> {
    const response = await fetch(`/api/tags/${id}`, {
      method: 'DELETE',
    })
    if (!response.ok) {
      throw new Error('删除标签失败')
    }
  }
}

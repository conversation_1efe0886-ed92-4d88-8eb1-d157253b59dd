"use client";

import { usePromptStore } from "@/lib/store";
import { PromptCard } from "@/components/prompt-card";
import { EmptyState } from "@/components/empty-state";

export function PromptGrid() {
  const { filteredPrompts, searchQuery, selectedCategory, selectedTags } = usePromptStore();

  const prompts = filteredPrompts();

  if (prompts.length === 0) {
    return (
      <EmptyState 
        hasFilters={!!(searchQuery || selectedCategory || selectedTags.length > 0)}
      />
    );
  }

  return (
    <div className="responsive-grid fixed-height animate-fade-in">
      {prompts.map((prompt, index) => (
        <div
          key={prompt.id}
          className="card-container animate-slide-up"
          style={{
            animationDelay: `${index * 25}ms`,
            animationFillMode: 'both'
          }}
        >
          <PromptCard prompt={prompt} />
        </div>
      ))}
    </div>
  );
}

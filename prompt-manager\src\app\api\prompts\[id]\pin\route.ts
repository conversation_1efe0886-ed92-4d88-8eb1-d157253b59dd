import { NextRequest, NextResponse } from 'next/server'
import { promptsApi } from '@/lib/database'
import { invalidateCache, CACHE_CONFIG } from '@/lib/cache'

// POST /api/prompts/[id]/pin - 置顶/取消置顶提示词
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    
    if (!id) {
      return NextResponse.json(
        { error: '提示词 ID 不能为空' },
        { status: 400 }
      )
    }

    // 切换置顶状态
    const prompt = await promptsApi.togglePin(id)
    
    // 清除相关缓存
    invalidateCache(CACHE_CONFIG.INVALIDATION_PATTERNS.PROMPTS)
    
    return NextResponse.json({
      success: true,
      prompt,
      message: prompt.isPinned ? '已置顶' : '已取消置顶'
    })
  } catch (error) {
    console.error('切换置顶状态失败:', error)
    return NextResponse.json(
      { error: '操作失败' },
      { status: 500 }
    )
  }
}

// PUT /api/prompts/[id]/pin - 设置置顶状态
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const { isPinned } = body
    
    if (!id) {
      return NextResponse.json(
        { error: '提示词 ID 不能为空' },
        { status: 400 }
      )
    }

    if (typeof isPinned !== 'boolean') {
      return NextResponse.json(
        { error: '置顶状态必须是布尔值' },
        { status: 400 }
      )
    }

    // 设置置顶状态
    const prompt = await promptsApi.setPin(id, isPinned)
    
    // 清除相关缓存
    invalidateCache(CACHE_CONFIG.INVALIDATION_PATTERNS.PROMPTS)
    
    return NextResponse.json({
      success: true,
      prompt,
      message: isPinned ? '已置顶' : '已取消置顶'
    })
  } catch (error) {
    console.error('设置置顶状态失败:', error)
    return NextResponse.json(
      { error: '操作失败' },
      { status: 500 }
    )
  }
}

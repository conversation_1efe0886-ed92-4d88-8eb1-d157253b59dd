# 部署指南

本文档详细说明如何将提示词管理工具部署到 Vercel 和配置 Supabase 数据库。

## 前置准备

### 1. 创建 Supabase 项目

1. 访问 [Supabase](https://supabase.com) 并注册账号
2. 创建新项目
3. 等待项目初始化完成
4. 记录以下信息：
   - Project URL: `https://your-project.supabase.co`
   - Anon Key: 在 Settings > API 中找到

### 2. 设置数据库

1. 在 Supabase 控制台中，进入 SQL Editor
2. 复制 `supabase-schema.sql` 文件的内容
3. 粘贴并执行 SQL 脚本
4. 确认所有表都已创建成功

### 3. 配置行级安全策略（可选）

如果需要用户认证，可以配置更严格的 RLS 策略：

```sql
-- 示例：只允许认证用户访问
CREATE POLICY "Authenticated users only" ON prompts
FOR ALL USING (auth.role() = 'authenticated');
```

## Vercel 部署

### 方法一：通过 GitHub（推荐）

1. **推送代码到 GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin https://github.com/yourusername/prompt-manager.git
   git push -u origin main
   ```

2. **在 Vercel 中导入项目**
   - 访问 [Vercel](https://vercel.com)
   - 点击 "New Project"
   - 选择你的 GitHub 仓库
   - 点击 "Import"

3. **配置环境变量**
   在 Vercel 项目设置中添加以下环境变量：
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   ```

4. **部署**
   - 点击 "Deploy"
   - 等待构建完成

### 方法二：通过 Vercel CLI

1. **安装 Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **登录 Vercel**
   ```bash
   vercel login
   ```

3. **部署项目**
   ```bash
   vercel
   ```

4. **设置环境变量**
   ```bash
   vercel env add NEXT_PUBLIC_SUPABASE_URL
   vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY
   ```

5. **重新部署**
   ```bash
   vercel --prod
   ```

## 环境变量配置

### 必需的环境变量

| 变量名 | 描述 | 示例 |
|--------|------|------|
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase 项目 URL | `https://abc123.supabase.co` |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Supabase 匿名密钥 | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |

### 可选的环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `NEXT_PUBLIC_APP_NAME` | 应用名称 | `提示词管理工具` |
| `NEXT_PUBLIC_APP_DESCRIPTION` | 应用描述 | `现代化的提示词管理和组织工具` |

## 域名配置

### 自定义域名

1. 在 Vercel 项目设置中点击 "Domains"
2. 添加你的自定义域名
3. 按照提示配置 DNS 记录
4. 等待 SSL 证书自动配置

### DNS 配置示例

对于域名 `prompts.yourdomain.com`：

```
Type: CNAME
Name: prompts
Value: cname.vercel-dns.com
```

## 性能优化

### 1. 启用 Vercel Analytics

```bash
npm install @vercel/analytics
```

在 `app/layout.tsx` 中添加：

```tsx
import { Analytics } from '@vercel/analytics/react'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  )
}
```

### 2. 配置缓存策略

在 `next.config.ts` 中添加：

```typescript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    ppr: true,
  },
  images: {
    domains: ['your-supabase-project.supabase.co'],
  },
}

module.exports = nextConfig
```

## 监控和维护

### 1. 错误监控

建议集成 Sentry 进行错误监控：

```bash
npm install @sentry/nextjs
```

### 2. 性能监控

使用 Vercel Analytics 和 Web Vitals 监控性能。

### 3. 数据库监控

在 Supabase 控制台中监控：
- 数据库性能
- API 使用情况
- 存储使用量

## 故障排除

### 常见问题

1. **构建失败**
   - 检查环境变量是否正确设置
   - 确认所有依赖都已安装

2. **数据库连接失败**
   - 验证 Supabase URL 和密钥
   - 检查 RLS 策略配置

3. **样式问题**
   - 确认 Tailwind CSS 配置正确
   - 检查 ShadcnUI 组件是否正确导入

### 调试技巧

1. **查看构建日志**
   ```bash
   vercel logs
   ```

2. **本地测试生产构建**
   ```bash
   npm run build
   npm run start
   ```

3. **检查环境变量**
   ```bash
   vercel env ls
   ```

## 安全考虑

### 1. 环境变量安全

- 不要在客户端代码中暴露敏感信息
- 使用 `NEXT_PUBLIC_` 前缀的变量会暴露给客户端

### 2. Supabase 安全

- 配置适当的 RLS 策略
- 定期轮换 API 密钥
- 监控 API 使用情况

### 3. Vercel 安全

- 启用 Vercel 的安全功能
- 配置适当的 CORS 策略
- 使用 HTTPS（Vercel 默认启用）

## 备份和恢复

### 数据库备份

Supabase 提供自动备份功能，也可以手动导出数据：

```sql
-- 导出提示词数据
COPY (SELECT * FROM prompts) TO '/tmp/prompts_backup.csv' WITH CSV HEADER;
```

### 代码备份

确保代码推送到 Git 仓库，并定期创建发布版本。

## 更新和维护

### 1. 依赖更新

定期更新依赖：

```bash
npm update
npm audit fix
```

### 2. Next.js 更新

```bash
npm install next@latest react@latest react-dom@latest
```

### 3. 数据库迁移

当需要修改数据库结构时，创建迁移脚本并在 Supabase 中执行。

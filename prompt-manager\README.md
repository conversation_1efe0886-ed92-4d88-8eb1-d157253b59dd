# 提示词管理工具

一个现代化的提示词管理和组织工具，基于 Next.js 15 + ShadcnUI + Supabase 构建。

## 功能特性

### 🎯 核心功能
- **提示词展示**: 响应式卡片网格布局，支持悬停效果和快速操作
- **详情查看**: 模态框显示完整内容，支持 Markdown 渲染
- **一键复制**: 复制提示词内容到剪贴板，自动统计使用次数
- **分类管理**: 左侧边栏分类导航，支持颜色标识
- **搜索功能**: 实时搜索，支持标题、内容、标签模糊匹配
- **编辑功能**: 完整的 CRUD 操作，支持 Markdown 编辑
- **标签系统**: 多标签支持，灵活的内容组织

### 🎨 界面设计
- **现代化 UI**: 基于 ShadcnUI 组件库，简洁美观
- **响应式设计**: 完美适配桌面端、平板和移动设备
- **多彩配色**: 柔和的色彩搭配，支持分类和标签颜色自定义
- **微动画**: 丰富的悬停效果和过渡动画
- **中文界面**: 完全中文化的用户界面

### 📊 统计功能
- **使用统计**: 每个提示词的使用次数追踪
- **时间信息**: 创建时间和最后修改时间显示
- **数据概览**: 侧边栏显示总体统计信息

## 技术栈

- **前端框架**: Next.js 15 (App Router)
- **UI 组件**: ShadcnUI + Tailwind CSS v4
- **状态管理**: Zustand
- **表单处理**: React Hook Form + Zod
- **数据库**: Supabase (PostgreSQL)
- **图标库**: Lucide React
- **Markdown**: React Markdown
- **通知**: Sonner
- **语言**: TypeScript

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
复制 `.env.example` 到 `.env.local` 并填入你的 Supabase 配置：

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. 设置数据库
在 Supabase SQL 编辑器中执行 `supabase-schema.sql` 文件来创建数据库表结构。

### 4. 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 数据库架构

### 表结构
- **prompts**: 提示词主表
- **categories**: 分类表，支持层级结构
- **tags**: 标签表
- **prompt_tags**: 提示词标签关联表

### 示例数据
数据库脚本包含了示例分类、标签和提示词数据，帮助你快速开始使用。

## 部署

### Vercel 部署
1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署完成

### 环境变量配置
确保在部署平台中设置以下环境变量：
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`

## 使用指南

### 创建提示词
1. 点击右上角"新建提示词"按钮
2. 填写标题、描述和内容
3. 选择分类和标签
4. 保存

### 搜索和筛选
- 使用顶部搜索框进行实时搜索
- 点击筛选按钮设置高级筛选条件
- 在侧边栏点击分类或标签进行快速筛选

### 管理分类和标签
- 在侧边栏点击分类旁的"+"按钮添加新分类
- 支持为分类设置颜色和图标
- 在编辑提示词时可以添加新标签

## 开发

### 项目结构
```
src/
├── app/                 # Next.js App Router
├── components/          # React 组件
│   ├── ui/             # ShadcnUI 组件
│   └── ...             # 自定义组件
├── lib/                # 工具函数和配置
│   ├── supabase.ts     # Supabase 配置和 API
│   ├── store.ts        # Zustand 状态管理
│   └── utils.ts        # 工具函数
└── hooks/              # 自定义 Hooks
```

### 开发规范
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 代码规范
- 组件采用函数式组件 + Hooks
- 使用 JSDoc 注释规范

## 许可证

MIT License

"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { usePromptStore, useTagStore } from "@/lib/store";
import { tagsApi } from "@/lib/api";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Save, 
  Hash
} from "lucide-react";
import { toast } from "sonner";

// 表单验证模式
const tagSchema = z.object({
  name: z.string().min(1, "标签名称不能为空").max(50, "标签名称过长"),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, "请选择有效的颜色"),
});

type TagFormData = z.infer<typeof tagSchema>;

// 预设颜色
const PRESET_COLORS = [
  "#EF4444", // 红色
  "#F59E0B", // 黄色
  "#10B981", // 绿色
  "#3B82F6", // 蓝色
  "#8B5CF6", // 紫色
  "#F97316", // 橙色
  "#06B6D4", // 青色
  "#84CC16", // 青绿色
  "#EC4899", // 粉色
  "#6B7280", // 灰色
];

export function TagDialog() {
  const {
    tags,
    setTags,
    prompts,
    setPrompts,
  } = usePromptStore();

  const {
    isTagDialogOpen,
    selectedTagForEdit,
    closeTagDialog,
  } = useTagStore();

  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<TagFormData>({
    resolver: zodResolver(tagSchema),
    defaultValues: {
      name: "",
      color: PRESET_COLORS[0],
    },
  });

  // 当选中的标签改变时，更新表单
  useEffect(() => {
    if (selectedTagForEdit) {
      reset({
        name: selectedTagForEdit.name,
        color: selectedTagForEdit.color,
      });
    } else {
      reset({
        name: "",
        color: PRESET_COLORS[0],
      });
    }
  }, [selectedTagForEdit, reset]);

  // 提交表单
  const onSubmit = async (data: TagFormData) => {
    try {
      setIsLoading(true);

      if (selectedTagForEdit) {
        // 更新现有标签
        const updatedTag = await tagsApi.update(selectedTagForEdit.id, data);

        // 更新本地状态
        const updatedTags = tags.map(tag =>
          tag.id === selectedTagForEdit.id ? updatedTag : tag
        );
        setTags(updatedTags);

        // 同时更新相关提示词的标签信息
        const updatedPrompts = prompts.map(prompt => ({
          ...prompt,
          tags: prompt.tags?.map(tag =>
            tag.id === selectedTagForEdit.id ? updatedTag : tag
          ) || []
        }));
        setPrompts(updatedPrompts);

        toast.success("标签已更新");
      } else {
        // 创建新标签
        const newTag = await tagsApi.create(data);

        // 添加到本地状态
        setTags([...tags, newTag]);

        toast.success("标签已创建");
      }

      closeTagDialog();
    } catch (error) {
      console.error("保存标签失败:", error);
      toast.error("保存标签失败");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isTagDialogOpen} onOpenChange={closeTagDialog}>
      <DialogContent className="max-w-md bg-slate-50 border-4 border-slate-800 shadow-2xl ring-8 ring-primary/20">
        <DialogHeader>
          <DialogTitle>
            {selectedTagForEdit ? "编辑标签" : "新建标签"}
          </DialogTitle>
          <DialogDescription>
            {selectedTagForEdit ? "修改标签的名称和颜色" : "创建一个新的标签来标记你的提示词"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* 标签名称 */}
          <div className="space-y-2">
            <Label htmlFor="name">标签名称 *</Label>
            <Input
              id="name"
              placeholder="输入标签名称..."
              {...register("name")}
            />
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>

          {/* 颜色选择 */}
          <div className="space-y-2">
            <Label>颜色</Label>
            <div className="flex flex-wrap gap-2">
              {PRESET_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  className={`w-8 h-8 rounded-full border-2 transition-all ${
                    watch("color") === color 
                      ? "border-foreground scale-110" 
                      : "border-muted hover:scale-105"
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => setValue("color", color)}
                />
              ))}
            </div>
            <Input
              type="color"
              value={watch("color")}
              onChange={(e) => setValue("color", e.target.value)}
              className="w-20 h-10"
            />
          </div>

          {/* 预览 */}
          <div className="space-y-2">
            <Label>预览</Label>
            <div className="flex items-center gap-2 p-3 border rounded-lg">
              <Hash 
                className="h-4 w-4" 
                style={{ color: watch("color") }}
              />
              <span>#{watch("name") || "标签名称"}</span>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={closeTagDialog}
              disabled={isLoading}
            >
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                "保存中..."
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {selectedTagForEdit ? "更新" : "创建"}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

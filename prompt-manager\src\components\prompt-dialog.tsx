"use client";

import { useState, useMemo, memo } from "react";
import { usePromptStore } from "@/lib/store";
import { promptsApi } from "@/lib/api";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Copy,
  Edit,
  TrendingUp,
  Calendar,
  Eye,
  Hash,
  Folder,
  Download,
  Share,
  MoreHorizontal,
  Code,
  FileText,
  ExternalLink,
  Clock,
  User,
} from "lucide-react";
import { toast } from "sonner";
import { PromptForm } from "./prompt-form";
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import remarkBreaks from 'remark-breaks';

const PromptDialogComponent = () => {
  const {
    isPromptDialogOpen,
    selectedPrompt,
    isEditMode,
    closePromptDialog,
    openPromptDialog,
    setPrompts,
    prompts,
  } = usePromptStore();

  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("content");

  // 缓存 Markdown 组件配置以提高性能
  const markdownComponents = useMemo(() => ({
    code: ({ node, inline, className, children, ...props }: any) => {
      const match = /language-(\w+)/.exec(className || '');
      const language = match ? match[1] : '';

      if (inline) {
        return (
          <code
            className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono border border-gray-200 dark:border-gray-600 text-gray-800 dark:text-gray-200"
            {...props}
          >
            {children}
          </code>
        );
      }

      return (
        <div className="relative group mb-4">
          {/* 复制按钮 - 改进可见性 */}
          <Button
            size="sm"
            variant="secondary"
            className="absolute top-2 right-2 opacity-90 hover:opacity-100 transition-all duration-200 z-20 bg-white/90 hover:bg-white text-gray-700 border border-gray-300 shadow-sm hover:shadow-md backdrop-blur-sm"
            onClick={() => handleCopyCode(String(children))}
          >
            <Copy className="h-3.5 w-3.5 mr-1.5" />
            <span className="text-xs font-medium">复制代码</span>
          </Button>

          {/* 代码块容器 */}
          <div className="relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
            {/* 语言标识 */}
            {language && (
              <div className="absolute top-2 left-3 z-10">
                <span className="text-xs px-2 py-1 bg-gray-600/80 text-gray-200 rounded backdrop-blur-sm font-mono">
                  {language}
                </span>
              </div>
            )}

            <SyntaxHighlighter
              style={oneDark}
              language={language || 'text'}
              PreTag="div"
              className="!mt-0 !mb-0 overflow-x-auto"
              customStyle={{
                padding: '1.5rem',
                paddingTop: '2.5rem', // 为复制按钮和语言标识留出空间
                fontSize: '14px',
                lineHeight: '1.5',
                margin: 0,
                background: '#1e1e1e'
              }}
              {...props}
            >
              {String(children).replace(/\n$/, '')}
            </SyntaxHighlighter>
          </div>
        </div>
      );
    },
    p: ({ children }: any) => (
      <p className="mb-4 leading-relaxed text-base">{children}</p>
    ),
    h1: ({ children }: any) => (
      <h1 className="text-2xl font-bold mb-4 mt-6 first:mt-0">{children}</h1>
    ),
    h2: ({ children }: any) => (
      <h2 className="text-xl font-semibold mb-3 mt-5">{children}</h2>
    ),
    h3: ({ children }: any) => (
      <h3 className="text-lg font-medium mb-2 mt-4">{children}</h3>
    ),
    ul: ({ children }: any) => (
      <ul className="mb-4 pl-6 space-y-2">{children}</ul>
    ),
    ol: ({ children }: any) => (
      <ol className="mb-4 pl-6 space-y-2">{children}</ol>
    ),
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-primary pl-4 italic my-4 text-muted-foreground">
        {children}
      </blockquote>
    ),
  }), []);

  // 缓存渲染的 Markdown 内容
  const renderedMarkdown = useMemo(() => {
    if (!selectedPrompt?.content) return null;

    return (
      <ReactMarkdown
        components={markdownComponents}
        remarkPlugins={[remarkBreaks]}
      >
        {selectedPrompt.content}
      </ReactMarkdown>
    );
  }, [selectedPrompt?.content, markdownComponents]);

  // 复制提示词内容
  const handleCopy = async () => {
    if (!selectedPrompt) return;

    try {
      await navigator.clipboard.writeText(selectedPrompt.content);

      // 增加使用次数
      await promptsApi.incrementUsage(selectedPrompt.id);

      // 更新本地状态
      const updatedPrompts = prompts.map(p =>
        p.id === selectedPrompt.id
          ? { ...p, usageCount: p.usageCount + 1 }
          : p
      );
      setPrompts(updatedPrompts);

      toast.success("已复制到剪贴板");
    } catch (error) {
      console.error("复制失败:", error);
      toast.error("复制失败");
    }
  };

  // 复制代码块
  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success("代码已复制到剪贴板");
    } catch (error) {
      console.error("复制失败:", error);
      toast.error("复制失败");
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 导出提示词
  const handleExport = () => {
    if (!selectedPrompt) return;

    const exportData = {
      title: selectedPrompt.title,
      description: selectedPrompt.description,
      content: selectedPrompt.content,
      category: selectedPrompt.category?.name,
      tags: selectedPrompt.tags?.map(tag => tag.name),
      created_at: selectedPrompt.createdAt,
      updated_at: selectedPrompt.updatedAt,
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${selectedPrompt.title}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("提示词已导出");
  };

  // 只有在明确的编辑模式或新建模式下才渲染表单
  // 避免在关闭过程中的中间状态触发新建模式
  if (isEditMode || (!selectedPrompt && isPromptDialogOpen)) {
    return (
      <Dialog open={isPromptDialogOpen} onOpenChange={(open) => {
        if (!open) {
          closePromptDialog();
        }
      }}>
        <DialogContent className="max-w-[50vw] max-h-[90vh] w-[50vw] overflow-hidden flex flex-col bg-slate-50 border-4 border-slate-800 shadow-2xl animate-scale-in ring-8 ring-primary/20">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-foreground">
              {selectedPrompt ? "编辑提示词" : "新建提示词"}
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              {selectedPrompt ? "修改提示词的内容、分类和标签" : "创建一个新的提示词"}
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-auto">
            <PromptForm
              prompt={selectedPrompt}
              onSuccess={closePromptDialog}
            />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // 如果没有选中的提示词，不渲染详情模式
  if (!selectedPrompt) {
    return null;
  }

  return (
    <Dialog open={isPromptDialogOpen} onOpenChange={(open) => {
      if (!open) {
        closePromptDialog();
      }
    }}>
      <DialogContent className="max-w-[50vw] max-h-[95vh] w-[50vw] h-[95vh] overflow-hidden flex flex-col bg-white border-2 border-slate-300 shadow-xl animate-scale-in"
        style={{ maxHeight: '95vh', height: '95vh' }}>

        {/* 头部区域 */}
        <DialogHeader className="flex-shrink-0 pb-0">
          <DialogTitle className="text-lg font-medium text-slate-600 mb-4">
            提示词详情
          </DialogTitle>
          <DialogDescription className="sr-only">
            查看和管理提示词的详细内容、标签和元数据信息
          </DialogDescription>

          {/* 提示词标题 */}
          <div className="mb-4">
            <h2 className="text-2xl font-bold text-slate-900 mb-3">
              {selectedPrompt.title}
            </h2>

            {/* 元数据信息行 */}
            <div className="flex items-center gap-6 text-sm text-slate-600">
              {/* 分类 */}
              {selectedPrompt.category && (
                <div className="flex items-center gap-1">
                  <div
                    className="w-3 h-3 rounded"
                    style={{ backgroundColor: selectedPrompt.category.color }}
                  />
                  <span>{selectedPrompt.category.name}</span>
                </div>
              )}

              {/* 使用次数 */}
              <div className="flex items-center gap-1">
                <span className="text-slate-500">使用</span>
                <span className="font-medium">{selectedPrompt.usageCount} 次</span>
              </div>

              {/* 创建时间 */}
              <div className="flex items-center gap-1">
                <span className="text-slate-500">创建于</span>
                <span>{formatDate(selectedPrompt.createdAt)}</span>
              </div>

              {/* 更新时间 */}
              <div className="flex items-center gap-1">
                <span className="text-slate-500">更新于</span>
                <span>{formatDate(selectedPrompt.updatedAt)}</span>
              </div>
            </div>
          </div>

          {/* 标签区域 */}
          {selectedPrompt.tags && selectedPrompt.tags.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-slate-700 mb-2">标签</h3>
              <div className="flex flex-wrap gap-2">
                {selectedPrompt.tags.map((tag) => (
                  <span
                    key={tag.id}
                    className="px-2 py-1 text-sm bg-slate-100 text-slate-700 rounded"
                  >
                    #{tag.name}
                  </span>
                ))}
              </div>
            </div>
          )}
        </DialogHeader>

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden flex flex-col">
          <div className="flex-shrink-0 mb-4">
            <h3 className="text-sm font-medium text-slate-700 mb-3">提示词内容</h3>
          </div>
          <div className="bg-slate-50 rounded-lg p-4 border border-slate-200 flex-1 flex flex-col">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
                <TabsList className="grid w-full grid-cols-2 mb-4 bg-white">
                  <TabsTrigger value="content" className="gap-2 data-[state=active]:bg-primary data-[state=active]:text-white">
                    <FileText className="h-4 w-4" />
                    Markdown 预览
                  </TabsTrigger>
                  <TabsTrigger value="raw" className="gap-2 data-[state=active]:bg-primary data-[state=active]:text-white">
                    <Code className="h-4 w-4" />
                    原始文本
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="content" className="flex-1 overflow-hidden mt-0">
                  <ScrollArea className="h-full" style={{ height: 'calc(95vh - 300px)' }}>
                    <div className="prose prose-sm max-w-none p-4 bg-white rounded border">
                      {renderedMarkdown}
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="raw" className="flex-1 overflow-hidden mt-0">
                  <ScrollArea className="h-full" style={{ height: 'calc(95vh - 300px)' }}>
                    <div className="bg-slate-800 text-slate-100 p-4 rounded font-mono text-sm leading-relaxed whitespace-pre-wrap">
                      {selectedPrompt.content}
                    </div>
                  </ScrollArea>
                </TabsContent>
              </Tabs>
            </div>
        </div>

        {/* 底部操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t border-slate-200">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // 直接切换到编辑模式，避免状态冲突
                if (selectedPrompt) {
                  openPromptDialog(selectedPrompt, true);
                }
              }}
              className="gap-2"
            >
              <Edit className="h-4 w-4" />
              编辑
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              className="gap-2 text-red-600 border-red-200 hover:bg-red-50"
            >
              <Download className="h-4 w-4" />
              删除
            </Button>
          </div>

          <Button
            onClick={handleCopy}
            disabled={isLoading}
            className="gap-2 bg-green-600 hover:bg-green-700 text-white"
          >
            <Copy className="h-4 w-4" />
            复制内容
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// 使用 memo 优化性能，避免不必要的重新渲染
export const PromptDialog = memo(PromptDialogComponent);

"use client";

import { useState } from "react";
import { usePromptStore } from "@/lib/store";
import { promptsApi } from "@/lib/api";
import type { Prompt } from "@/lib/api";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Copy,
  Edit,
  Eye,
  MoreVertical,
  Trash2,
  Calendar,
  TrendingUp,
  Pin,
  PinOff,
  CheckSquare,
  Square
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface PromptCardProps {
  prompt: Prompt;
}

export function PromptCard({ prompt }: PromptCardProps) {
  const {
    openPromptDialog,
    setPrompts,
    prompts,
    // 🚀 批量操作相关状态
    isBatchMode,
    selectedPromptIds,
    togglePromptSelection
  } = usePromptStore();
  const [isLoading, setIsLoading] = useState(false);
  const [isCopying, setIsCopying] = useState(false);

  // 是否被选中
  const isSelected = selectedPromptIds.includes(prompt.id);

  // 复制提示词内容
  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (isCopying) return; // 防止重复点击

    setIsCopying(true);

    try {
      // 立即复制到剪贴板并显示反馈
      await navigator.clipboard.writeText(prompt.content);
      toast.success("已复制到剪贴板");

      // 在后台异步增加使用次数，不阻塞用户体验
      promptsApi.incrementUsage(prompt.id).then(() => {
        // 更新本地状态
        const updatedPrompts = prompts.map(p =>
          p.id === prompt.id
            ? { ...p, usageCount: p.usageCount + 1 }
            : p
        );
        setPrompts(updatedPrompts);
      }).catch((error) => {
        console.error("更新使用次数失败:", error);
        // 不显示错误给用户，因为复制已经成功了
      });

    } catch (error) {
      console.error("复制失败:", error);
      toast.error("复制失败");
    } finally {
      // 短暂延迟后重置状态，防止按钮闪烁
      setTimeout(() => setIsCopying(false), 500);
    }
  };

  // 查看详情
  const handleView = (e: React.MouseEvent) => {
    e.stopPropagation();
    openPromptDialog(prompt, false);
  };

  // 卡片点击处理
  const handleCardClick = () => {
    if (isBatchMode) {
      togglePromptSelection(prompt.id);
    } else {
      openPromptDialog(prompt);
    }
  };

  // 编辑提示词
  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    openPromptDialog(prompt, true);
  };

  // 🚀 置顶/取消置顶
  const handleTogglePin = async (e: React.MouseEvent) => {
    e.stopPropagation();

    try {
      const updatedPrompt = await promptsApi.togglePin(prompt.id);

      // 更新本地状态
      const updatedPrompts = prompts.map(p =>
        p.id === prompt.id ? updatedPrompt : p
      );
      setPrompts(updatedPrompts);

      toast.success(updatedPrompt.isPinned ? "已置顶" : "已取消置顶");
    } catch (error) {
      console.error("切换置顶状态失败:", error);
      toast.error("操作失败");
    }
  };

  // 🚀 切换选择状态
  const handleToggleSelection = (e: React.MouseEvent) => {
    e.stopPropagation();
    togglePromptSelection(prompt.id);
  };

  // 删除提示词
  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (!confirm("确定要删除这个提示词吗？")) {
      return;
    }

    try {
      setIsLoading(true);
      await promptsApi.delete(prompt.id);
      
      // 更新本地状态
      const updatedPrompts = prompts.filter(p => p.id !== prompt.id);
      setPrompts(updatedPrompts);
      
      toast.success("提示词已删除");
    } catch (error) {
      console.error("删除失败:", error);
      toast.error("删除失败");
    } finally {
      setIsLoading(false);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // 智能截断文本 - 优先在句号、感叹号、问号处截断
  const smartTruncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;

    // 在maxLength范围内查找最后的句号、感叹号或问号
    const truncated = text.substring(0, maxLength);
    const lastSentenceEnd = Math.max(
      truncated.lastIndexOf('。'),
      truncated.lastIndexOf('！'),
      truncated.lastIndexOf('？'),
      truncated.lastIndexOf('.'),
      truncated.lastIndexOf('!'),
      truncated.lastIndexOf('?')
    );

    // 如果找到句子结尾且位置合理（不少于maxLength的60%），在那里截断
    if (lastSentenceEnd > maxLength * 0.6) {
      return text.substring(0, lastSentenceEnd + 1);
    }

    // 否则在最后一个空格处截断，避免截断单词
    const lastSpace = truncated.lastIndexOf(' ');
    if (lastSpace > maxLength * 0.8) {
      return text.substring(0, lastSpace) + '...';
    }

    // 最后选择：直接截断并添加省略号
    return text.substring(0, maxLength) + '...';
  };

  return (
    <Card
      className={cn(
        "professional-card group cursor-pointer relative h-full flex flex-col",
        "hover:shadow-lg hover:shadow-primary/10 hover:-translate-y-1 hover:scale-[1.02]",
        "transition-all duration-300 ease-out",
        "bg-card border-2",
        "hover:border-primary/30",
        isLoading && "opacity-50 pointer-events-none",
        // 🚀 批量模式样式
        isBatchMode && "hover:border-primary/50",
        isSelected && "border-primary bg-primary/5 shadow-lg shadow-primary/20 scale-[1.02] -translate-y-1"
      )}
      onClick={handleCardClick}
    >
      {/* 🚀 批量选择框 */}
      {isBatchMode && (
        <div className="absolute top-3 right-3 z-20">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleSelection}
            className="h-6 w-6 p-0 rounded-full bg-white/90 hover:bg-white border border-border/50 shadow-sm"
          >
            {isSelected ? (
              <CheckSquare className="h-4 w-4 text-primary" />
            ) : (
              <Square className="h-4 w-4 text-muted-foreground" />
            )}
          </Button>
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-2 flex-1">
            <h3 className="font-bold text-base md:text-lg leading-tight line-clamp-2 text-foreground">
              {prompt.title}
            </h3>
            {prompt.isPinned && (
              <Pin className="h-4 w-4 text-amber-500 shrink-0 mt-0.5" title="已置顶" />
            )}
          </div>

          {!isBatchMode && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "h-8 w-8 p-0 shrink-0 rounded-full",
                    "opacity-0 group-hover:opacity-100 transition-opacity duration-200",
                    "hover:bg-accent"
                  )}
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="border">
              <DropdownMenuItem onClick={handleView}>
                <Eye className="h-4 w-4 mr-2" />
                查看详情
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="h-4 w-4 mr-2" />
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleCopy} disabled={isCopying}>
                {isCopying ? (
                  <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                ) : (
                  <Copy className="h-4 w-4 mr-2" />
                )}
                {isCopying ? "复制中..." : "复制内容"}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleTogglePin}>
                {prompt.isPinned ? (
                  <PinOff className="h-4 w-4 mr-2" />
                ) : (
                  <Pin className="h-4 w-4 mr-2" />
                )}
                {prompt.isPinned ? "取消置顶" : "置顶"}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        {/* 描述区域 - 固定高度确保对齐 */}
        <div className="mt-2 h-12 flex items-start">
          {prompt.description ? (
            <p className="text-xs md:text-sm text-muted-foreground line-clamp-2 leading-relaxed">
              {prompt.description}
            </p>
          ) : (
            <div className="h-12" />
          )}
        </div>
      </CardHeader>

      <CardContent className="pb-3 flex-1 flex flex-col">
        {/* 内容预览 - 固定高度确保对齐 */}
        <div className="mb-4 h-20 flex items-start">
          <div className="text-xs md:text-sm text-muted-foreground leading-relaxed p-3 rounded-md bg-muted border relative overflow-hidden w-full">
            <div className="line-clamp-3">
              {smartTruncateText(prompt.content, 150)}
            </div>
          </div>
        </div>

        {/* 分类和标签 */}
        <div className="flex flex-wrap gap-1.5 md:gap-2 mt-auto">
          {prompt.category && (
            <Badge
              variant="secondary"
              className="text-xs font-medium px-2.5 py-1 rounded-md border"
              style={{
                backgroundColor: `${prompt.category.color}10`,
                color: prompt.category.color,
                borderColor: `${prompt.category.color}30`
              }}
            >
              {prompt.category.name}
            </Badge>
          )}

          {prompt.tags?.slice(0, 3).map((tag) => (
            <Badge
              key={tag.id}
              variant="outline"
              className="text-xs font-medium px-2.5 py-1 rounded-md border"
              style={{
                borderColor: `${tag.color}40`,
                color: tag.color,
                backgroundColor: `${tag.color}08`
              }}
            >
              #{tag.name}
            </Badge>
          ))}

          {prompt.tags && prompt.tags.length > 3 && (
            <Badge
              variant="outline"
              className="text-xs font-medium px-2.5 py-1 rounded-md border-dashed border-muted-foreground/30 text-muted-foreground"
            >
              +{prompt.tags.length - 3}
            </Badge>
          )}
        </div>
      </CardContent>

      <CardFooter className="pt-0 flex items-center justify-between relative z-10">
        {/* 统计信息 */}
        <div className="flex items-center gap-4 text-xs text-muted-foreground/80">
          <div className="flex items-center gap-1.5 px-2 py-1 rounded-full bg-primary/10 border border-primary/20">
            <TrendingUp className="h-3 w-3 text-primary" />
            <span className="font-medium">{prompt.usageCount}</span>
          </div>
          <div className="flex items-center gap-1.5 px-2 py-1 rounded-full bg-muted border border-border">
            <Calendar className="h-3 w-3 text-muted-foreground" />
            <span className="font-medium">{formatDate(prompt.updatedAt)}</span>
          </div>
        </div>

        {/* 快速操作按钮 */}
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-0 translate-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            disabled={isCopying}
            className={cn(
              "h-8 w-8 p-0 rounded-full transition-all duration-200",
              "hover:bg-accent",
              isCopying && "bg-accent"
            )}
            title={isCopying ? "复制中..." : "复制内容"}
          >
            {isCopying ? (
              <div className="h-3 w-3 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            ) : (
              <Copy className="h-3 w-3" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEdit}
            className={cn(
              "h-8 w-8 p-0 rounded-full transition-all duration-200",
              "hover:bg-accent"
            )}
            title="编辑"
          >
            <Edit className="h-3 w-3" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}

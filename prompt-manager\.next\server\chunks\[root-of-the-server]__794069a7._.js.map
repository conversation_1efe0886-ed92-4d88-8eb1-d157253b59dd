{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/lib/database.ts"], "sourcesContent": ["import { prisma } from './prisma'\nimport type { Category, Tag, Prompt, PromptTag } from '@prisma/client'\n\n// 扩展类型定义，包含关联数据\nexport type PromptWithRelations = Prompt & {\n  category: Category | null\n  promptTags: (PromptTag & {\n    tag: Tag\n  })[]\n  // 为了兼容性，添加 tags 字段\n  tags: Tag[]\n}\n\nexport type CategoryWithChildren = Category & {\n  children: Category[]\n}\n\n// 提示词 API\nexport const promptsApi = {\n  // 获取所有提示词（智能排序：置顶优先，然后按使用频率排序）\n  async getAll(): Promise<PromptWithRelations[]> {\n    const prompts = await prisma.prompt.findMany({\n      include: {\n        category: true,\n        promptTags: {\n          include: {\n            tag: true\n          }\n        }\n      },\n      orderBy: [\n        { isPinned: 'desc' },      // 置顶的优先\n        { pinnedAt: 'desc' },      // 置顶时间倒序\n        { usageCount: 'desc' },    // 使用频率高的优先\n        { updatedAt: 'desc' }      // 最后更新时间倒序\n      ]\n    })\n\n    // 转换数据格式以兼容现有代码\n    return prompts.map(prompt => ({\n      ...prompt,\n      tags: prompt.promptTags.map(pt => pt.tag)\n    }))\n  },\n\n  // 根据 ID 获取提示词\n  async getById(id: string): Promise<PromptWithRelations | null> {\n    const prompt = await prisma.prompt.findUnique({\n      where: { id },\n      include: {\n        category: true,\n        promptTags: {\n          include: {\n            tag: true\n          }\n        }\n      }\n    })\n\n    if (!prompt) return null\n\n    // 转换数据格式以兼容现有代码\n    return {\n      ...prompt,\n      tags: prompt.promptTags.map(pt => pt.tag)\n    }\n  },\n\n  // 创建提示词\n  async create(data: {\n    title: string\n    content: string\n    description?: string\n    categoryId?: string\n    tagIds?: string[]\n  }): Promise<PromptWithRelations> {\n    const { tagIds, ...promptData } = data\n\n    const prompt = await prisma.prompt.create({\n      data: {\n        ...promptData,\n        promptTags: tagIds ? {\n          create: tagIds.map(tagId => ({\n            tagId\n          }))\n        } : undefined\n      },\n      include: {\n        category: true,\n        promptTags: {\n          include: {\n            tag: true\n          }\n        }\n      }\n    })\n\n    // 转换数据格式以兼容现有代码\n    return {\n      ...prompt,\n      tags: prompt.promptTags.map(pt => pt.tag)\n    }\n  },\n\n  // 更新提示词\n  async update(id: string, data: {\n    title?: string\n    content?: string\n    description?: string\n    categoryId?: string\n    tagIds?: string[]\n  }): Promise<PromptWithRelations> {\n    const { tagIds, ...promptData } = data\n\n    // 如果有标签更新，先删除现有关联，再创建新关联\n    if (tagIds !== undefined) {\n      await prisma.promptTag.deleteMany({\n        where: { promptId: id }\n      })\n    }\n\n    const prompt = await prisma.prompt.update({\n      where: { id },\n      data: {\n        ...promptData,\n        promptTags: tagIds ? {\n          create: tagIds.map(tagId => ({\n            tagId\n          }))\n        } : undefined\n      },\n      include: {\n        category: true,\n        promptTags: {\n          include: {\n            tag: true\n          }\n        }\n      }\n    })\n\n    // 转换数据格式以兼容现有代码\n    return {\n      ...prompt,\n      tags: prompt.promptTags.map(pt => pt.tag)\n    }\n  },\n\n  // 删除提示词\n  async delete(id: string): Promise<void> {\n    await prisma.prompt.delete({\n      where: { id }\n    })\n  },\n\n  // 增加使用次数\n  async incrementUsage(id: string): Promise<void> {\n    await prisma.prompt.update({\n      where: { id },\n      data: {\n        usageCount: {\n          increment: 1\n        }\n      }\n    })\n  },\n\n  // 置顶/取消置顶提示词\n  async togglePin(id: string): Promise<PromptWithRelations> {\n    const prompt = await prisma.prompt.findUnique({\n      where: { id }\n    })\n\n    if (!prompt) {\n      throw new Error('提示词不存在')\n    }\n\n    const updatedPrompt = await prisma.prompt.update({\n      where: { id },\n      data: {\n        isPinned: !prompt.isPinned,\n        pinnedAt: !prompt.isPinned ? new Date() : null\n      },\n      include: {\n        category: true,\n        promptTags: {\n          include: {\n            tag: true\n          }\n        }\n      }\n    })\n\n    return {\n      ...updatedPrompt,\n      tags: updatedPrompt.promptTags.map(pt => pt.tag)\n    }\n  },\n\n  // 设置置顶状态\n  async setPin(id: string, isPinned: boolean): Promise<PromptWithRelations> {\n    const updatedPrompt = await prisma.prompt.update({\n      where: { id },\n      data: {\n        isPinned,\n        pinnedAt: isPinned ? new Date() : null\n      },\n      include: {\n        category: true,\n        promptTags: {\n          include: {\n            tag: true\n          }\n        }\n      }\n    })\n\n    return {\n      ...updatedPrompt,\n      tags: updatedPrompt.promptTags.map(pt => pt.tag)\n    }\n  },\n\n  // 按分类筛选（智能排序：置顶优先，然后按使用频率排序）\n  async getByCategory(categoryId: string): Promise<PromptWithRelations[]> {\n    const prompts = await prisma.prompt.findMany({\n      where: { categoryId },\n      include: {\n        category: true,\n        promptTags: {\n          include: {\n            tag: true\n          }\n        }\n      },\n      orderBy: [\n        { isPinned: 'desc' },      // 置顶的优先\n        { pinnedAt: 'desc' },      // 置顶时间倒序\n        { usageCount: 'desc' },    // 使用频率高的优先\n        { updatedAt: 'desc' }      // 最后更新时间倒序\n      ]\n    })\n\n    // 转换数据格式以兼容现有代码\n    return prompts.map(prompt => ({\n      ...prompt,\n      tags: prompt.promptTags.map(pt => pt.tag)\n    }))\n  },\n\n  // 全文搜索（标题+内容+描述）\n  async search(keyword: string): Promise<PromptWithRelations[]> {\n    if (!keyword.trim()) {\n      return this.getAll(); // 如果没有关键词，返回所有提示词\n    }\n\n    const prompts = await prisma.prompt.findMany({\n      where: {\n        OR: [\n          { title: { contains: keyword, mode: 'insensitive' } },\n          { content: { contains: keyword, mode: 'insensitive' } },\n          { description: { contains: keyword, mode: 'insensitive' } }\n        ]\n      },\n      include: {\n        category: true,\n        promptTags: {\n          include: {\n            tag: true\n          }\n        }\n      },\n      orderBy: [\n        { isPinned: 'desc' },      // 置顶的优先\n        { pinnedAt: 'desc' },      // 置顶时间倒序\n        { usageCount: 'desc' },    // 使用频率高的优先\n        { updatedAt: 'desc' }      // 最后更新时间倒序\n      ]\n    })\n\n    // 转换数据格式以兼容现有代码\n    return prompts.map(prompt => ({\n      ...prompt,\n      tags: prompt.promptTags.map(pt => pt.tag)\n    }))\n  },\n\n  // 批量删除提示词\n  async deleteMany(ids: string[]): Promise<void> {\n    await prisma.prompt.deleteMany({\n      where: {\n        id: {\n          in: ids\n        }\n      }\n    })\n  },\n\n  // 批量移动分类\n  async updateCategory(ids: string[], categoryId: string | null): Promise<void> {\n    await prisma.prompt.updateMany({\n      where: {\n        id: {\n          in: ids\n        }\n      },\n      data: {\n        categoryId: categoryId === 'none' ? null : categoryId\n      }\n    })\n  }\n}\n\n// 分类 API\nexport const categoriesApi = {\n  // 获取所有分类\n  async getAll(): Promise<CategoryWithChildren[]> {\n    return await prisma.category.findMany({\n      include: {\n        children: true\n      },\n      orderBy: {\n        sortOrder: 'asc'\n      }\n    })\n  },\n\n  // 根据 ID 获取分类\n  async getById(id: string): Promise<Category | null> {\n    return await prisma.category.findUnique({\n      where: { id }\n    })\n  },\n\n  // 创建分类\n  async create(data: {\n    name: string\n    color?: string\n    icon?: string\n    parentId?: string\n    sortOrder?: number\n  }): Promise<Category> {\n    return await prisma.category.create({\n      data\n    })\n  },\n\n  // 更新分类\n  async update(id: string, data: {\n    name?: string\n    color?: string\n    icon?: string\n    parentId?: string\n    sortOrder?: number\n  }): Promise<Category> {\n    return await prisma.category.update({\n      where: { id },\n      data\n    })\n  },\n\n  // 删除分类\n  async delete(id: string): Promise<void> {\n    await prisma.category.delete({\n      where: { id }\n    })\n  }\n}\n\n// 标签 API\nexport const tagsApi = {\n  // 获取所有标签\n  async getAll(): Promise<Tag[]> {\n    return await prisma.tag.findMany({\n      orderBy: {\n        name: 'asc'\n      }\n    })\n  },\n\n  // 根据 ID 获取标签\n  async getById(id: string): Promise<Tag | null> {\n    return await prisma.tag.findUnique({\n      where: { id }\n    })\n  },\n\n  // 创建标签\n  async create(data: {\n    name: string\n    color?: string\n  }): Promise<Tag> {\n    return await prisma.tag.create({\n      data\n    })\n  },\n\n  // 更新标签\n  async update(id: string, data: {\n    name?: string\n    color?: string\n  }): Promise<Tag> {\n    return await prisma.tag.update({\n      where: { id },\n      data\n    })\n  },\n\n  // 删除标签\n  async delete(id: string): Promise<void> {\n    await prisma.tag.delete({\n      where: { id }\n    })\n  }\n}\n\n// 导出类型\nexport type { Category, Tag, Prompt, PromptTag, PromptWithRelations, CategoryWithChildren }\n"], "names": [], "mappings": ";;;;;AAAA;;AAkBO,MAAM,aAAa;IACxB,+BAA+B;IAC/B,MAAM;QACJ,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C,SAAS;gBACP,UAAU;gBACV,YAAY;oBACV,SAAS;wBACP,KAAK;oBACP;gBACF;YACF;YACA,SAAS;gBACP;oBAAE,UAAU;gBAAO;gBACnB;oBAAE,UAAU;gBAAO;gBACnB;oBAAE,YAAY;gBAAO;gBACrB;oBAAE,WAAW;gBAAO,EAAO,WAAW;aACvC;QACH;QAEA,gBAAgB;QAChB,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B,GAAG,MAAM;gBACT,MAAM,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,GAAG;YAC1C,CAAC;IACH;IAEA,cAAc;IACd,MAAM,SAAQ,EAAU;QACtB,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YAC5C,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,UAAU;gBACV,YAAY;oBACV,SAAS;wBACP,KAAK;oBACP;gBACF;YACF;QACF;QAEA,IAAI,CAAC,QAAQ,OAAO;QAEpB,gBAAgB;QAChB,OAAO;YACL,GAAG,MAAM;YACT,MAAM,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,GAAG;QAC1C;IACF;IAEA,QAAQ;IACR,MAAM,QAAO,IAMZ;QACC,MAAM,EAAE,MAAM,EAAE,GAAG,YAAY,GAAG;QAElC,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YACxC,MAAM;gBACJ,GAAG,UAAU;gBACb,YAAY,SAAS;oBACnB,QAAQ,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;4BAC3B;wBACF,CAAC;gBACH,IAAI;YACN;YACA,SAAS;gBACP,UAAU;gBACV,YAAY;oBACV,SAAS;wBACP,KAAK;oBACP;gBACF;YACF;QACF;QAEA,gBAAgB;QAChB,OAAO;YACL,GAAG,MAAM;YACT,MAAM,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,GAAG;QAC1C;IACF;IAEA,QAAQ;IACR,MAAM,QAAO,EAAU,EAAE,IAMxB;QACC,MAAM,EAAE,MAAM,EAAE,GAAG,YAAY,GAAG;QAElC,yBAAyB;QACzB,IAAI,WAAW,WAAW;YACxB,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,UAAU,CAAC;gBAChC,OAAO;oBAAE,UAAU;gBAAG;YACxB;QACF;QAEA,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YACxC,OAAO;gBAAE;YAAG;YACZ,MAAM;gBACJ,GAAG,UAAU;gBACb,YAAY,SAAS;oBACnB,QAAQ,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;4BAC3B;wBACF,CAAC;gBACH,IAAI;YACN;YACA,SAAS;gBACP,UAAU;gBACV,YAAY;oBACV,SAAS;wBACP,KAAK;oBACP;gBACF;YACF;QACF;QAEA,gBAAgB;QAChB,OAAO;YACL,GAAG,MAAM;YACT,MAAM,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,GAAG;QAC1C;IACF;IAEA,QAAQ;IACR,MAAM,QAAO,EAAU;QACrB,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YACzB,OAAO;gBAAE;YAAG;QACd;IACF;IAEA,SAAS;IACT,MAAM,gBAAe,EAAU;QAC7B,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YACzB,OAAO;gBAAE;YAAG;YACZ,MAAM;gBACJ,YAAY;oBACV,WAAW;gBACb;YACF;QACF;IACF;IAEA,aAAa;IACb,MAAM,WAAU,EAAU;QACxB,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YAC5C,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YAC/C,OAAO;gBAAE;YAAG;YACZ,MAAM;gBACJ,UAAU,CAAC,OAAO,QAAQ;gBAC1B,UAAU,CAAC,OAAO,QAAQ,GAAG,IAAI,SAAS;YAC5C;YACA,SAAS;gBACP,UAAU;gBACV,YAAY;oBACV,SAAS;wBACP,KAAK;oBACP;gBACF;YACF;QACF;QAEA,OAAO;YACL,GAAG,aAAa;YAChB,MAAM,cAAc,UAAU,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,GAAG;QACjD;IACF;IAEA,SAAS;IACT,MAAM,QAAO,EAAU,EAAE,QAAiB;QACxC,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YAC/C,OAAO;gBAAE;YAAG;YACZ,MAAM;gBACJ;gBACA,UAAU,WAAW,IAAI,SAAS;YACpC;YACA,SAAS;gBACP,UAAU;gBACV,YAAY;oBACV,SAAS;wBACP,KAAK;oBACP;gBACF;YACF;QACF;QAEA,OAAO;YACL,GAAG,aAAa;YAChB,MAAM,cAAc,UAAU,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,GAAG;QACjD;IACF;IAEA,6BAA6B;IAC7B,MAAM,eAAc,UAAkB;QACpC,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C,OAAO;gBAAE;YAAW;YACpB,SAAS;gBACP,UAAU;gBACV,YAAY;oBACV,SAAS;wBACP,KAAK;oBACP;gBACF;YACF;YACA,SAAS;gBACP;oBAAE,UAAU;gBAAO;gBACnB;oBAAE,UAAU;gBAAO;gBACnB;oBAAE,YAAY;gBAAO;gBACrB;oBAAE,WAAW;gBAAO,EAAO,WAAW;aACvC;QACH;QAEA,gBAAgB;QAChB,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B,GAAG,MAAM;gBACT,MAAM,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,GAAG;YAC1C,CAAC;IACH;IAEA,iBAAiB;IACjB,MAAM,QAAO,OAAe;QAC1B,IAAI,CAAC,QAAQ,IAAI,IAAI;YACnB,OAAO,IAAI,CAAC,MAAM,IAAI,kBAAkB;QAC1C;QAEA,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C,OAAO;gBACL,IAAI;oBACF;wBAAE,OAAO;4BAAE,UAAU;4BAAS,MAAM;wBAAc;oBAAE;oBACpD;wBAAE,SAAS;4BAAE,UAAU;4BAAS,MAAM;wBAAc;oBAAE;oBACtD;wBAAE,aAAa;4BAAE,UAAU;4BAAS,MAAM;wBAAc;oBAAE;iBAC3D;YACH;YACA,SAAS;gBACP,UAAU;gBACV,YAAY;oBACV,SAAS;wBACP,KAAK;oBACP;gBACF;YACF;YACA,SAAS;gBACP;oBAAE,UAAU;gBAAO;gBACnB;oBAAE,UAAU;gBAAO;gBACnB;oBAAE,YAAY;gBAAO;gBACrB;oBAAE,WAAW;gBAAO,EAAO,WAAW;aACvC;QACH;QAEA,gBAAgB;QAChB,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B,GAAG,MAAM;gBACT,MAAM,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,GAAG;YAC1C,CAAC;IACH;IAEA,UAAU;IACV,MAAM,YAAW,GAAa;QAC5B,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YAC7B,OAAO;gBACL,IAAI;oBACF,IAAI;gBACN;YACF;QACF;IACF;IAEA,SAAS;IACT,MAAM,gBAAe,GAAa,EAAE,UAAyB;QAC3D,MAAM,sHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YAC7B,OAAO;gBACL,IAAI;oBACF,IAAI;gBACN;YACF;YACA,MAAM;gBACJ,YAAY,eAAe,SAAS,OAAO;YAC7C;QACF;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,SAAS;IACT,MAAM;QACJ,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACpC,SAAS;gBACP,UAAU;YACZ;YACA,SAAS;gBACP,WAAW;YACb;QACF;IACF;IAEA,aAAa;IACb,MAAM,SAAQ,EAAU;QACtB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YACtC,OAAO;gBAAE;YAAG;QACd;IACF;IAEA,OAAO;IACP,MAAM,QAAO,IAMZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAClC;QACF;IACF;IAEA,OAAO;IACP,MAAM,QAAO,EAAU,EAAE,IAMxB;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAClC,OAAO;gBAAE;YAAG;YACZ;QACF;IACF;IAEA,OAAO;IACP,MAAM,QAAO,EAAU;QACrB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,OAAO;gBAAE;YAAG;QACd;IACF;AACF;AAGO,MAAM,UAAU;IACrB,SAAS;IACT,MAAM;QACJ,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,GAAG,CAAC,QAAQ,CAAC;YAC/B,SAAS;gBACP,MAAM;YACR;QACF;IACF;IAEA,aAAa;IACb,MAAM,SAAQ,EAAU;QACtB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,GAAG,CAAC,UAAU,CAAC;YACjC,OAAO;gBAAE;YAAG;QACd;IACF;IAEA,OAAO;IACP,MAAM,QAAO,IAGZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,GAAG,CAAC,MAAM,CAAC;YAC7B;QACF;IACF;IAEA,OAAO;IACP,MAAM,QAAO,EAAU,EAAE,IAGxB;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,GAAG,CAAC,MAAM,CAAC;YAC7B,OAAO;gBAAE;YAAG;YACZ;QACF;IACF;IAEA,OAAO;IACP,MAAM,QAAO,EAAU;QACrB,MAAM,sHAAA,CAAA,SAAM,CAAC,GAAG,CAAC,MAAM,CAAC;YACtB,OAAO;gBAAE;YAAG;QACd;IACF;AACF", "debugId": null}}, {"offset": {"line": 499, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment2/prompt-manager/src/app/api/prompts/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { promptsApi } from '@/lib/database'\n\n// GET /api/prompts/[id] - 获取单个提示词\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const prompt = await promptsApi.getById(params.id)\n    \n    if (!prompt) {\n      return NextResponse.json(\n        { error: '提示词不存在' },\n        { status: 404 }\n      )\n    }\n    \n    return NextResponse.json(prompt)\n  } catch (error) {\n    console.error('获取提示词失败:', error)\n    return NextResponse.json(\n      { error: '获取提示词失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT /api/prompts/[id] - 更新提示词\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const body = await request.json()\n    const { title, content, description, categoryId, tagIds } = body\n    \n    const prompt = await promptsApi.update(params.id, {\n      title,\n      content,\n      description,\n      categoryId: categoryId === 'none' ? undefined : categoryId,\n      tagIds\n    })\n    \n    return NextResponse.json(prompt)\n  } catch (error) {\n    console.error('更新提示词失败:', error)\n    return NextResponse.json(\n      { error: '更新提示词失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE /api/prompts/[id] - 删除提示词\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    await promptsApi.delete(params.id)\n    return NextResponse.json({ success: true })\n  } catch (error) {\n    console.error('删除提示词失败:', error)\n    return NextResponse.json(\n      { error: '删除提示词失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,SAAS,MAAM,wHAAA,CAAA,aAAU,CAAC,OAAO,CAAC,OAAO,EAAE;QAEjD,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAS,GAClB;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAU,GACnB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;QAE5D,MAAM,SAAS,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YAChD;YACA;YACA;YACA,YAAY,eAAe,SAAS,YAAY;YAChD;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAU,GACnB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,OAAO,EAAE;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAU,GACnB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
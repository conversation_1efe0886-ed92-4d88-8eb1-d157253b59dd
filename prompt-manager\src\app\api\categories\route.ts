import { NextRequest, NextResponse } from 'next/server'
import { categoriesApi } from '@/lib/database'
import { withCache, createCachedResponse, invalidateCache, CACHE_KEYS, CACHE_CONFIG } from '@/lib/cache'

// GET /api/categories - 获取所有分类（带缓存优化）
export async function GET() {
  try {
    // 使用缓存装饰器，5分钟缓存
    const getCachedCategories = withCache(
      CACHE_KEYS.CATEGORIES,
      CACHE_CONFIG.READ_TTL.CATEGORIES
    )(async () => {
      return await categoriesApi.getAll();
    });

    const categories = await getCachedCategories();

    // 返回带缓存头的响应
    return createCachedResponse(categories, CACHE_CONFIG.READ_TTL.CATEGORIES);
  } catch (error) {
    console.error('获取分类失败:', error)
    return NextResponse.json(
      { error: '获取分类失败' },
      { status: 500 }
    )
  }
}

// POST /api/categories - 创建新分类（带缓存失效）
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, color, icon, parentId, sortOrder } = body

    if (!name) {
      return NextResponse.json(
        { error: '分类名称不能为空' },
        { status: 400 }
      )
    }

    const category = await categoriesApi.create({
      name,
      color,
      icon,
      parentId,
      sortOrder
    })

    // 创建成功后，清除相关缓存
    invalidateCache(CACHE_CONFIG.INVALIDATION_PATTERNS.CATEGORIES);

    return NextResponse.json(category)
  } catch (error) {
    console.error('创建分类失败:', error)
    return NextResponse.json(
      { error: '创建分类失败' },
      { status: 500 }
    )
  }
}

// 提示词管理工具 Prisma Schema
// 用于 Vercel Postgres 数据库

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_PRISMA_URL")
  directUrl = env("DATABASE_URL")
}

// 分类表
model Category {
  id        String   @id @default(cuid())
  name      String   @db.VarChar(100)
  color     String   @default("#3B82F6") @db.VarChar(7)
  icon      String   @default("folder") @db.VarChar(50)
  parentId  String?  @map("parent_id")
  sortOrder Int      @default(0) @map("sort_order")
  createdAt DateTime @default(now()) @map("created_at")

  // 关系
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id], onDelete: Cascade)
  children Category[] @relation("CategoryHierarchy")
  prompts  Prompt[]

  @@index([parentId])
  @@index([sortOrder])
  @@map("categories")
}

// 标签表
model Tag {
  id        String   @id @default(cuid())
  name      String   @unique @db.VarChar(50)
  color     String   @default("#6B7280") @db.VarChar(7)
  createdAt DateTime @default(now()) @map("created_at")

  // 关系
  promptTags PromptTag[]

  @@map("tags")
}

// 提示词表
model Prompt {
  id          String    @id @default(cuid())
  title       String    @db.VarChar(255)
  content     String    @db.Text
  description String?   @db.Text
  categoryId  String?   @map("category_id")
  usageCount  Int       @default(0) @map("usage_count")
  isPinned    Boolean   @default(false) @map("is_pinned")
  pinnedAt    DateTime? @map("pinned_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // 关系
  category   Category?   @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  promptTags PromptTag[]

  @@index([categoryId])
  @@index([title])
  @@index([updatedAt])
  @@index([isPinned])
  @@index([usageCount])
  @@map("prompts")
}

// 提示词标签关联表
model PromptTag {
  promptId String @map("prompt_id")
  tagId    String @map("tag_id")

  // 关系
  prompt Prompt @relation(fields: [promptId], references: [id], onDelete: Cascade)
  tag    Tag    @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([promptId, tagId])
  @@index([promptId])
  @@index([tagId])
  @@map("prompt_tags")
}

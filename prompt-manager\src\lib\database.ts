import { prisma } from './prisma'
import type { Category, Tag, Prompt, PromptTag } from '@prisma/client'

// 扩展类型定义，包含关联数据
export type PromptWithRelations = Prompt & {
  category: Category | null
  promptTags: (PromptTag & {
    tag: Tag
  })[]
  // 为了兼容性，添加 tags 字段
  tags: Tag[]
}

export type CategoryWithChildren = Category & {
  children: Category[]
}

// 提示词 API
export const promptsApi = {
  // 获取所有提示词（智能排序：置顶优先，然后按使用频率排序）
  async getAll(): Promise<PromptWithRelations[]> {
    const prompts = await prisma.prompt.findMany({
      include: {
        category: true,
        promptTags: {
          include: {
            tag: true
          }
        }
      },
      orderBy: [
        { isPinned: 'desc' },      // 置顶的优先
        { pinnedAt: 'desc' },      // 置顶时间倒序
        { usageCount: 'desc' },    // 使用频率高的优先
        { updatedAt: 'desc' }      // 最后更新时间倒序
      ]
    })

    // 转换数据格式以兼容现有代码
    return prompts.map(prompt => ({
      ...prompt,
      tags: prompt.promptTags.map(pt => pt.tag)
    }))
  },

  // 根据 ID 获取提示词
  async getById(id: string): Promise<PromptWithRelations | null> {
    const prompt = await prisma.prompt.findUnique({
      where: { id },
      include: {
        category: true,
        promptTags: {
          include: {
            tag: true
          }
        }
      }
    })

    if (!prompt) return null

    // 转换数据格式以兼容现有代码
    return {
      ...prompt,
      tags: prompt.promptTags.map(pt => pt.tag)
    }
  },

  // 创建提示词
  async create(data: {
    title: string
    content: string
    description?: string
    categoryId?: string
    tagIds?: string[]
  }): Promise<PromptWithRelations> {
    const { tagIds, ...promptData } = data

    const prompt = await prisma.prompt.create({
      data: {
        ...promptData,
        promptTags: tagIds ? {
          create: tagIds.map(tagId => ({
            tagId
          }))
        } : undefined
      },
      include: {
        category: true,
        promptTags: {
          include: {
            tag: true
          }
        }
      }
    })

    // 转换数据格式以兼容现有代码
    return {
      ...prompt,
      tags: prompt.promptTags.map(pt => pt.tag)
    }
  },

  // 更新提示词
  async update(id: string, data: {
    title?: string
    content?: string
    description?: string
    categoryId?: string
    tagIds?: string[]
  }): Promise<PromptWithRelations> {
    const { tagIds, ...promptData } = data

    // 如果有标签更新，先删除现有关联，再创建新关联
    if (tagIds !== undefined) {
      await prisma.promptTag.deleteMany({
        where: { promptId: id }
      })
    }

    const prompt = await prisma.prompt.update({
      where: { id },
      data: {
        ...promptData,
        promptTags: tagIds ? {
          create: tagIds.map(tagId => ({
            tagId
          }))
        } : undefined
      },
      include: {
        category: true,
        promptTags: {
          include: {
            tag: true
          }
        }
      }
    })

    // 转换数据格式以兼容现有代码
    return {
      ...prompt,
      tags: prompt.promptTags.map(pt => pt.tag)
    }
  },

  // 删除提示词
  async delete(id: string): Promise<void> {
    await prisma.prompt.delete({
      where: { id }
    })
  },

  // 增加使用次数
  async incrementUsage(id: string): Promise<void> {
    await prisma.prompt.update({
      where: { id },
      data: {
        usageCount: {
          increment: 1
        }
      }
    })
  },

  // 置顶/取消置顶提示词
  async togglePin(id: string): Promise<PromptWithRelations> {
    const prompt = await prisma.prompt.findUnique({
      where: { id }
    })

    if (!prompt) {
      throw new Error('提示词不存在')
    }

    const updatedPrompt = await prisma.prompt.update({
      where: { id },
      data: {
        isPinned: !prompt.isPinned,
        pinnedAt: !prompt.isPinned ? new Date() : null
      },
      include: {
        category: true,
        promptTags: {
          include: {
            tag: true
          }
        }
      }
    })

    return {
      ...updatedPrompt,
      tags: updatedPrompt.promptTags.map(pt => pt.tag)
    }
  },

  // 设置置顶状态
  async setPin(id: string, isPinned: boolean): Promise<PromptWithRelations> {
    const updatedPrompt = await prisma.prompt.update({
      where: { id },
      data: {
        isPinned,
        pinnedAt: isPinned ? new Date() : null
      },
      include: {
        category: true,
        promptTags: {
          include: {
            tag: true
          }
        }
      }
    })

    return {
      ...updatedPrompt,
      tags: updatedPrompt.promptTags.map(pt => pt.tag)
    }
  },

  // 按分类筛选（智能排序：置顶优先，然后按使用频率排序）
  async getByCategory(categoryId: string): Promise<PromptWithRelations[]> {
    const prompts = await prisma.prompt.findMany({
      where: { categoryId },
      include: {
        category: true,
        promptTags: {
          include: {
            tag: true
          }
        }
      },
      orderBy: [
        { isPinned: 'desc' },      // 置顶的优先
        { pinnedAt: 'desc' },      // 置顶时间倒序
        { usageCount: 'desc' },    // 使用频率高的优先
        { updatedAt: 'desc' }      // 最后更新时间倒序
      ]
    })

    // 转换数据格式以兼容现有代码
    return prompts.map(prompt => ({
      ...prompt,
      tags: prompt.promptTags.map(pt => pt.tag)
    }))
  },

  // 全文搜索（标题+内容+描述）
  async search(keyword: string): Promise<PromptWithRelations[]> {
    if (!keyword.trim()) {
      return this.getAll(); // 如果没有关键词，返回所有提示词
    }

    const prompts = await prisma.prompt.findMany({
      where: {
        OR: [
          { title: { contains: keyword, mode: 'insensitive' } },
          { content: { contains: keyword, mode: 'insensitive' } },
          { description: { contains: keyword, mode: 'insensitive' } }
        ]
      },
      include: {
        category: true,
        promptTags: {
          include: {
            tag: true
          }
        }
      },
      orderBy: [
        { isPinned: 'desc' },      // 置顶的优先
        { pinnedAt: 'desc' },      // 置顶时间倒序
        { usageCount: 'desc' },    // 使用频率高的优先
        { updatedAt: 'desc' }      // 最后更新时间倒序
      ]
    })

    // 转换数据格式以兼容现有代码
    return prompts.map(prompt => ({
      ...prompt,
      tags: prompt.promptTags.map(pt => pt.tag)
    }))
  },

  // 批量删除提示词
  async deleteMany(ids: string[]): Promise<void> {
    await prisma.prompt.deleteMany({
      where: {
        id: {
          in: ids
        }
      }
    })
  },

  // 批量移动分类
  async updateCategory(ids: string[], categoryId: string | null): Promise<void> {
    await prisma.prompt.updateMany({
      where: {
        id: {
          in: ids
        }
      },
      data: {
        categoryId: categoryId === 'none' ? null : categoryId
      }
    })
  }
}

// 分类 API
export const categoriesApi = {
  // 获取所有分类
  async getAll(): Promise<CategoryWithChildren[]> {
    return await prisma.category.findMany({
      include: {
        children: true
      },
      orderBy: {
        sortOrder: 'asc'
      }
    })
  },

  // 根据 ID 获取分类
  async getById(id: string): Promise<Category | null> {
    return await prisma.category.findUnique({
      where: { id }
    })
  },

  // 创建分类
  async create(data: {
    name: string
    color?: string
    icon?: string
    parentId?: string
    sortOrder?: number
  }): Promise<Category> {
    return await prisma.category.create({
      data
    })
  },

  // 更新分类
  async update(id: string, data: {
    name?: string
    color?: string
    icon?: string
    parentId?: string
    sortOrder?: number
  }): Promise<Category> {
    return await prisma.category.update({
      where: { id },
      data
    })
  },

  // 删除分类
  async delete(id: string): Promise<void> {
    await prisma.category.delete({
      where: { id }
    })
  }
}

// 标签 API
export const tagsApi = {
  // 获取所有标签
  async getAll(): Promise<Tag[]> {
    return await prisma.tag.findMany({
      orderBy: {
        name: 'asc'
      }
    })
  },

  // 根据 ID 获取标签
  async getById(id: string): Promise<Tag | null> {
    return await prisma.tag.findUnique({
      where: { id }
    })
  },

  // 创建标签
  async create(data: {
    name: string
    color?: string
  }): Promise<Tag> {
    return await prisma.tag.create({
      data
    })
  },

  // 更新标签
  async update(id: string, data: {
    name?: string
    color?: string
  }): Promise<Tag> {
    return await prisma.tag.update({
      where: { id },
      data
    })
  },

  // 删除标签
  async delete(id: string): Promise<void> {
    await prisma.tag.delete({
      where: { id }
    })
  }
}

// 导出类型
export type { Category, Tag, Prompt, PromptTag, PromptWithRelations, CategoryWithChildren }

"use client";

import { usePromptStore } from "@/lib/store";
import { Button } from "@/components/ui/button";
import { 
  FileText, 
  Search, 
  Plus,
  Filter
} from "lucide-react";

interface EmptyStateProps {
  hasFilters: boolean;
}

export function EmptyState({ hasFilters }: EmptyStateProps) {
  const { 
    openPromptDialog, 
    setSearchQuery, 
    setSelectedCategory, 
    setSelectedTags 
  } = usePromptStore();

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedCategory(null);
    setSelectedTags([]);
  };

  if (hasFilters) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
          <Search className="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-semibold mb-2">未找到匹配的提示词</h3>
        <p className="text-muted-foreground mb-4 max-w-md">
          没有找到符合当前筛选条件的提示词。试试调整搜索关键词或筛选条件。
        </p>
        <div className="flex gap-2">
          <Button variant="outline" onClick={clearFilters}>
            <Filter className="h-4 w-4 mr-2" />
            清除筛选
          </Button>
          <Button onClick={() => openPromptDialog()}>
            <Plus className="h-4 w-4 mr-2" />
            新建提示词
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center h-64 text-center">
      <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
        <FileText className="h-8 w-8 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-semibold mb-2">还没有提示词</h3>
      <p className="text-muted-foreground mb-4 max-w-md">
        开始创建你的第一个提示词，构建你的个人提示词库。
      </p>
      <Button onClick={() => openPromptDialog()}>
        <Plus className="h-4 w-4 mr-2" />
        创建第一个提示词
      </Button>
    </div>
  );
}

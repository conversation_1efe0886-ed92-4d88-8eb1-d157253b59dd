import { NextRequest, NextResponse } from 'next/server'
import { tagsApi } from '@/lib/database'

// GET /api/tags/[id] - 获取单个标签
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const tag = await tagsApi.getById(params.id)
    
    if (!tag) {
      return NextResponse.json(
        { error: '标签不存在' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(tag)
  } catch (error) {
    console.error('获取标签失败:', error)
    return NextResponse.json(
      { error: '获取标签失败' },
      { status: 500 }
    )
  }
}

// PUT /api/tags/[id] - 更新标签
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { name, color } = body
    
    if (!name) {
      return NextResponse.json(
        { error: '标签名称不能为空' },
        { status: 400 }
      )
    }
    
    const tag = await tagsApi.update(params.id, {
      name,
      color
    })
    
    return NextResponse.json(tag)
  } catch (error) {
    console.error('更新标签失败:', error)
    return NextResponse.json(
      { error: '更新标签失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/tags/[id] - 删除标签
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await tagsApi.delete(params.id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('删除标签失败:', error)
    return NextResponse.json(
      { error: '删除标签失败' },
      { status: 500 }
    )
  }
}
